import { AgentConfig, AgentContext, ConversationMessage, ToolCall } from '../../types';
export interface AIResponse {
    content: string;
    toolCalls?: ToolCall[];
    usage?: {
        promptTokens: number;
        completionTokens: number;
        totalTokens: number;
    };
}
/**
 * Universal AI Provider using OpenAI SDK 5.0.0
 * Supports OpenAI, Deepseek, and Ollama through compatible APIs
 */
export declare class AIProvider {
    private client;
    private config;
    constructor(config: AgentConfig);
    private initializeClient;
    /**
     * Get AI response with streaming support and tool calls
     */
    getResponse(conversationHistory: ConversationMessage[], toolSchemas: any[], context: AgentContext): Promise<AIResponse>;
    /**
     * Get streaming AI response
     */
    getStreamingResponse(conversationHistory: ConversationMessage[], toolSchemas: any[], context: AgentContext, onChunk: (chunk: string) => void, onToolCall: (toolCall: ToolCall) => void): Promise<AIResponse>;
    /**
     * Format conversation messages for AI provider
     */
    private formatMessages;
    /**
     * Build comprehensive system prompt with context
     */
    private buildSystemPrompt;
    /**
     * Format tool schemas for AI provider
     */
    private formatToolSchemas;
    /**
     * Parse AI response and extract tool calls
     */
    private parseResponse;
    /**
     * Update provider configuration
     */
    updateConfig(newConfig: AgentConfig): void;
    /**
     * Test connection to AI provider
     */
    testConnection(): Promise<boolean>;
}
//# sourceMappingURL=AIProvider.d.ts.map