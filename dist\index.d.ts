export { AgentEngine } from './core/AgentEngine';
export { AIProvider } from './core/ai/AIProvider';
export { ToolRegistry } from './core/tools/ToolRegistry';
export { ContextManager } from './core/context/ContextManager';
export { ConfigManager } from './core/config/ConfigManager';
export { DiffTracker } from './core/diff/DiffTracker';
export { SemanticSearchEngine } from './core/semantic/SemanticSearchEngine';
export { GitManager } from './core/git/GitManager';
export { TerminalUI } from './ui/TerminalUI';
export * from './types';
export * from './tools/FileOperations';
export * from './tools/ShellCommands';
export * from './tools/GitOperations';
export * from './tools/SemanticSearch';
export * from './tools/ContextManagement';
export declare const VERSION = "1.0.0";
export declare const DEFAULT_CONFIG: {
    provider: "openai";
    model: string;
    temperature: number;
    maxTokens: number;
};
//# sourceMappingURL=index.d.ts.map