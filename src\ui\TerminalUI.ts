import readline from 'readline';
import chalk from 'chalk';
import ora, { Ora } from 'ora';
import { AgentEngine } from '../core/AgentEngine';
import { Tool<PERSON>all, ToolResult, ChangeRecord } from '../types';

export class TerminalUI {
  private agent: AgentEngine;
  private rl!: readline.Interface;
  private spinner: Ora;
  private isProcessing: boolean = false;

  constructor(agent: AgentEngine) {
    this.agent = agent;
    this.spinner = ora();
    this.setupReadline();
    this.setupEventHandlers();
  }

  private setupReadline(): void {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
      prompt: chalk.cyan('🤖 > ')
    });

    this.rl.on('line', async (input) => {
      const trimmedInput = input.trim();
      
      if (trimmedInput === '') {
        this.rl.prompt();
        return;
      }

      if (trimmedInput === 'exit' || trimmedInput === 'quit') {
        await this.shutdown();
        return;
      }

      if (trimmedInput === 'help') {
        this.showHelp();
        this.rl.prompt();
        return;
      }

      if (trimmedInput === 'status') {
        this.showStatus();
        this.rl.prompt();
        return;
      }

      if (trimmedInput === 'clear') {
        console.clear();
        this.showWelcome();
        this.rl.prompt();
        return;
      }

      if (trimmedInput.startsWith('config ')) {
        await this.handleConfigCommand(trimmedInput.substring(7));
        this.rl.prompt();
        return;
      }

      // Process user input
      await this.processUserInput(trimmedInput);
    });

    this.rl.on('close', () => {
      this.shutdown();
    });
  }

  private setupEventHandlers(): void {
    // Processing events
    this.agent.on('processing-started', (data) => {
      this.isProcessing = true;
      this.spinner.start(chalk.blue('🧠 Agent is thinking...'));
    });

    this.agent.on('processing-completed', () => {
      this.isProcessing = false;
      this.spinner.stop();
      console.log(chalk.green('✓ Task completed\n'));
      this.rl.prompt();
    });

    // Tool execution events
    this.agent.on('tool-execution-started', (toolCall: ToolCall) => {
      this.spinner.text = chalk.yellow(`🔧 Executing: ${toolCall.name}`);
    });

    this.agent.on('tool-execution-completed', (result: ToolResult) => {
      if (result.success) {
        console.log(chalk.green(`✓ ${result.metadata?.toolName || 'Tool'} completed`));
        
        // Show relevant output
        if (result.output && typeof result.output === 'object') {
          this.displayToolOutput(result);
        }
      }
    });

    this.agent.on('tool-execution-failed', (result: ToolResult) => {
      console.log(chalk.red(`✗ ${result.metadata?.toolName || 'Tool'} failed: ${result.error}`));
    });

    // Change tracking events
    this.agent.on('change-tracked', (change: ChangeRecord) => {
      this.displayChange(change);
    });

    // Git events
    this.agent.on('git-operation', (operation) => {
      console.log(chalk.magenta(`🔀 Git: ${operation.type} - ${operation.message || 'Operation completed'}`));
    });

    // Error events
    this.agent.on('error', (error) => {
      this.spinner.stop();
      console.log(chalk.red(`❌ Error: ${error.message}`));
      this.isProcessing = false;
      this.rl.prompt();
    });

    this.agent.on('fatal-error', (error) => {
      this.spinner.stop();
      console.log(chalk.red(`💥 Fatal Error: ${error.message}`));
      console.log(chalk.yellow('Agent is shutting down...'));
      this.shutdown();
    });
  }

  async start(): Promise<void> {
    this.showWelcome();
    this.rl.prompt();
  }

  private showWelcome(): void {
    console.log(chalk.blue.bold('\n🤖 Autonomous Agent CLI'));
    console.log(chalk.gray('Type your request and I\'ll execute it autonomously.'));
    console.log(chalk.gray('Commands: help, status, clear, config, exit\n'));
  }

  private showHelp(): void {
    console.log(chalk.blue('\n📖 Available Commands:'));
    console.log(chalk.white('  help     - Show this help message'));
    console.log(chalk.white('  status   - Show agent status and context'));
    console.log(chalk.white('  clear    - Clear the terminal'));
    console.log(chalk.white('  config   - Show configuration'));
    console.log(chalk.white('  exit     - Exit the agent\n'));
    
    console.log(chalk.blue('💡 Example Requests:'));
    console.log(chalk.white('  "Create a new React component called Button"'));
    console.log(chalk.white('  "Fix the authentication bug in login.js"'));
    console.log(chalk.white('  "Search for TODO comments in the codebase"'));
    console.log(chalk.white('  "Run the test suite and fix any failures"'));
    console.log(chalk.white('  "Commit all changes with a descriptive message"\n'));
  }

  private showStatus(): void {
    const context = this.agent.getContext();
    
    console.log(chalk.blue('\n📊 Agent Status:'));
    console.log(chalk.white(`  Session ID: ${context.sessionId}`));
    console.log(chalk.white(`  Working Directory: ${context.workingDirectory}`));
    console.log(chalk.white(`  Git Repository: ${context.gitRepository ? 'Yes' : 'No'}`));
    console.log(chalk.white(`  Active Tools: ${context.activeTools.length}`));
    console.log(chalk.white(`  Conversation History: ${context.conversationHistory.length} messages`));
    console.log(chalk.white(`  Changes Tracked: ${context.changeHistory.length}`));
    console.log(chalk.white(`  Processing: ${this.isProcessing ? 'Yes' : 'No'}\n`));
  }

  private async handleConfigCommand(command: string): Promise<void> {
    if (command === 'show') {
      // Show current configuration (without sensitive data)
      console.log(chalk.blue('\n⚙️ Current Configuration:'));
      console.log(chalk.white('  Provider: OpenAI')); // This would come from actual config
      console.log(chalk.white('  Model: gpt-4-turbo-preview'));
      console.log(chalk.white('  Temperature: 0.7\n'));
    } else {
      console.log(chalk.yellow('Available config commands: show\n'));
    }
  }

  private async processUserInput(input: string): Promise<void> {
    try {
      await this.agent.processInput(input);
    } catch (error) {
      this.spinner.stop();
      console.log(chalk.red(`Error processing input: ${(error as Error).message}`));
      this.isProcessing = false;
      this.rl.prompt();
    }
  }

  private displayToolOutput(result: ToolResult): void {
    const output = result.output;
    const toolName = result.metadata?.toolName;

    switch (toolName) {
      case 'read_file':
        console.log(chalk.gray(`📄 Read ${output.path} (${output.size} bytes)`));
        break;
      
      case 'write_file':
        console.log(chalk.gray(`💾 ${output.created ? 'Created' : 'Modified'} ${output.path} (${output.bytesWritten} bytes)`));
        break;
      
      case 'execute_command':
        console.log(chalk.gray(`⚡ Command: ${output.command}`));
        if (output.stdout) {
          console.log(chalk.white(output.stdout));
        }
        if (output.stderr) {
          console.log(chalk.red(output.stderr));
        }
        break;
      
      case 'list_directory':
        console.log(chalk.gray(`📁 Listed ${output.path} (${output.count} items)`));
        if (output.items && output.items.length <= 10) {
          output.items.forEach((item: any) => {
            const icon = item.type === 'directory' ? '📁' : '📄';
            console.log(chalk.white(`  ${icon} ${item.name}`));
          });
        }
        break;
      
      case 'search_files':
        console.log(chalk.gray(`🔍 Found ${output.totalMatches} matches for "${output.pattern}"`));
        if (output.results && output.results.length <= 5) {
          output.results.forEach((match: any) => {
            console.log(chalk.white(`  ${match.file}:${match.line} - ${match.content}`));
          });
        }
        break;
      
      default:
        // Generic output display
        if (typeof output === 'string') {
          console.log(chalk.white(output));
        }
    }
  }

  private displayChange(change: ChangeRecord): void {
    const icons = {
      'file-create': '📝',
      'file-modify': '✏️',
      'file-delete': '🗑️',
      'git-operation': '🔀'
    };

    const icon = icons[change.type] || '📄';
    console.log(chalk.cyan(`${icon} ${change.type}: ${change.path}`));

    // Show diff stats if available
    if (change.diff && change.diff.stats) {
      const stats = change.diff.stats;
      if (stats.additions > 0 || stats.deletions > 0) {
        console.log(chalk.green(`  +${stats.additions}`) + chalk.red(` -${stats.deletions}`));
      }
    }
  }

  private async shutdown(): Promise<void> {
    this.spinner.stop();
    console.log(chalk.yellow('\n👋 Shutting down agent...'));
    
    try {
      await this.agent.shutdown();
      console.log(chalk.green('✓ Agent shutdown complete'));
    } catch (error) {
      console.log(chalk.red(`Error during shutdown: ${(error as Error).message}`));
    }
    
    this.rl.close();
    process.exit(0);
  }
}
