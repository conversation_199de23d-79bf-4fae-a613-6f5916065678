import { z } from 'zod';
import { nanoid } from 'nanoid';
import { spawn, exec } from 'child_process';
import { promisify } from 'util';
import { <PERSON>l, <PERSON>l<PERSON><PERSON>ult, AgentContext, DebugSession, Breakpoint, StackFrame, Variable } from '../types';

const execAsync = promisify(exec);

// Analyze Error Tool
const analyzeErrorSchema = z.object({
  errorMessage: z.string().describe('Error message to analyze'),
  stackTrace: z.string().optional().describe('Stack trace if available'),
  filePath: z.string().optional().describe('File where error occurred'),
  lineNumber: z.number().optional().describe('Line number where error occurred'),
  context: z.string().optional().describe('Additional context about the error')
});

export const analyzeErrorTool: Tool = {
  name: 'analyze_error',
  description: 'Analyze an error and suggest potential fixes',
  schema: analyzeErrorSchema,
  category: 'debugging',
  async execute(params, context: AgentContext): Promise<ToolResult> {
    try {
      const analysis = {
        errorType: classifyError(params.errorMessage),
        severity: assessSeverity(params.errorMessage),
        possibleCauses: identifyPossibleCauses(params.errorMessage, params.stackTrace),
        suggestedFixes: generateFixSuggestions(params.errorMessage, params.stackTrace),
        relatedFiles: params.filePath ? [params.filePath] : [],
        debuggingSteps: generateDebuggingSteps(params.errorMessage)
      };

      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: true,
        output: analysis,
        timestamp: new Date(),
        metadata: {
          operation: 'analyze-error',
          errorType: analysis.errorType,
          severity: analysis.severity,
          requiresFollowup: true
        }
      };
    } catch (error) {
      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: false,
        output: null,
        error: `Failed to analyze error: ${(error as Error).message}`,
        timestamp: new Date()
      };
    }
  }
};

// Run Tests Tool
const runTestsSchema = z.object({
  testPath: z.string().optional().describe('Specific test file or directory to run'),
  testFramework: z.enum(['jest', 'mocha', 'vitest', 'pytest', 'go-test', 'cargo-test']).optional().describe('Test framework to use'),
  pattern: z.string().optional().describe('Test pattern to match'),
  verbose: z.boolean().default(false).optional().describe('Run tests in verbose mode'),
  coverage: z.boolean().default(false).optional().describe('Generate coverage report')
});

export const runTestsTool: Tool = {
  name: 'run_tests',
  description: 'Run tests to verify code functionality and identify issues',
  schema: runTestsSchema,
  category: 'debugging',
  async execute(params, context: AgentContext): Promise<ToolResult> {
    try {
      const testCommand = buildTestCommand(params);
      const { stdout, stderr } = await execAsync(testCommand, {
        cwd: context.workingDirectory,
        timeout: 60000
      });

      const testResults = parseTestOutput(stdout, stderr, params.testFramework);

      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: testResults.passed,
        output: {
          command: testCommand,
          results: testResults,
          stdout: stdout.substring(0, 2000), // Limit output size
          stderr: stderr.substring(0, 1000)
        },
        timestamp: new Date(),
        metadata: {
          operation: 'run-tests',
          testFramework: params.testFramework,
          passed: testResults.passed,
          failedTests: testResults.failed,
          continueExecution: !testResults.passed // Continue if tests failed
        }
      };
    } catch (error) {
      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: false,
        output: null,
        error: `Failed to run tests: ${(error as Error).message}`,
        timestamp: new Date()
      };
    }
  }
};

// Check Syntax Tool
const checkSyntaxSchema = z.object({
  filePath: z.string().describe('File to check for syntax errors'),
  language: z.string().optional().describe('Programming language (auto-detected if not provided)')
});

export const checkSyntaxTool: Tool = {
  name: 'check_syntax',
  description: 'Check file for syntax errors and issues',
  schema: checkSyntaxSchema,
  category: 'debugging',
  async execute(params, context: AgentContext): Promise<ToolResult> {
    try {
      const language = params.language || detectLanguageFromPath(params.filePath);
      const syntaxCommand = buildSyntaxCheckCommand(params.filePath, language);
      
      if (!syntaxCommand) {
        return {
          id: nanoid(),
          toolCallId: nanoid(),
          success: false,
          output: null,
          error: `Syntax checking not supported for language: ${language}`,
          timestamp: new Date()
        };
      }

      const { stdout, stderr } = await execAsync(syntaxCommand, {
        cwd: context.workingDirectory,
        timeout: 30000
      });

      const syntaxIssues = parseSyntaxOutput(stdout, stderr, language);

      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: syntaxIssues.length === 0,
        output: {
          filePath: params.filePath,
          language,
          issues: syntaxIssues,
          hasErrors: syntaxIssues.some(issue => issue.severity === 'error'),
          hasWarnings: syntaxIssues.some(issue => issue.severity === 'warning')
        },
        timestamp: new Date(),
        metadata: {
          operation: 'check-syntax',
          language,
          issueCount: syntaxIssues.length,
          requiresFollowup: syntaxIssues.length > 0
        }
      };
    } catch (error) {
      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: false,
        output: null,
        error: `Failed to check syntax: ${(error as Error).message}`,
        timestamp: new Date()
      };
    }
  }
};

// Generate Fix Tool
const generateFixSchema = z.object({
  errorMessage: z.string().describe('Error message to fix'),
  filePath: z.string().describe('File containing the error'),
  lineNumber: z.number().optional().describe('Line number where error occurred'),
  context: z.string().optional().describe('Code context around the error'),
  fixStrategy: z.enum(['conservative', 'aggressive', 'minimal']).default('conservative').describe('Fix strategy to use')
});

export const generateFixTool: Tool = {
  name: 'generate_fix',
  description: 'Generate and apply a fix for a specific error',
  schema: generateFixSchema,
  category: 'debugging',
  async execute(params, context: AgentContext): Promise<ToolResult> {
    try {
      // This would integrate with the AI provider to generate intelligent fixes
      const fixSuggestions = await generateIntelligentFix(params);
      
      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: true,
        output: {
          errorMessage: params.errorMessage,
          filePath: params.filePath,
          fixSuggestions,
          strategy: params.fixStrategy,
          requiresReview: params.fixStrategy === 'aggressive'
        },
        timestamp: new Date(),
        metadata: {
          operation: 'generate-fix',
          fixStrategy: params.fixStrategy,
          requiresFollowup: true, // Will need to apply the fix
          continueExecution: true
        }
      };
    } catch (error) {
      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: false,
        output: null,
        error: `Failed to generate fix: ${(error as Error).message}`,
        timestamp: new Date()
      };
    }
  }
};

// Monitor Process Tool
const monitorProcessSchema = z.object({
  processName: z.string().optional().describe('Process name to monitor'),
  pid: z.number().optional().describe('Process ID to monitor'),
  duration: z.number().default(30).optional().describe('Monitoring duration in seconds'),
  metrics: z.array(z.enum(['cpu', 'memory', 'io', 'network'])).default(['cpu', 'memory']).describe('Metrics to monitor')
});

export const monitorProcessTool: Tool = {
  name: 'monitor_process',
  description: 'Monitor a running process for performance issues',
  schema: monitorProcessSchema,
  category: 'debugging',
  async execute(params, context: AgentContext): Promise<ToolResult> {
    try {
      const monitoringResults = await monitorProcess(params);

      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: true,
        output: monitoringResults,
        timestamp: new Date(),
        metadata: {
          operation: 'monitor-process',
          duration: params.duration,
          metrics: params.metrics
        }
      };
    } catch (error) {
      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: false,
        output: null,
        error: `Failed to monitor process: ${(error as Error).message}`,
        timestamp: new Date()
      };
    }
  }
};

// Helper Functions
function classifyError(errorMessage: string): string {
  const errorPatterns = {
    'syntax': /syntax|parse|unexpected token/i,
    'runtime': /runtime|execution|null pointer|undefined/i,
    'type': /type|cannot convert|incompatible/i,
    'import': /import|module|cannot find/i,
    'network': /network|connection|timeout|refused/i,
    'permission': /permission|access|denied|unauthorized/i,
    'memory': /memory|out of|allocation/i
  };

  for (const [type, pattern] of Object.entries(errorPatterns)) {
    if (pattern.test(errorMessage)) {
      return type;
    }
  }
  return 'unknown';
}

function assessSeverity(errorMessage: string): 'low' | 'medium' | 'high' | 'critical' {
  if (/critical|fatal|crash|segmentation fault/i.test(errorMessage)) return 'critical';
  if (/error|exception|failed/i.test(errorMessage)) return 'high';
  if (/warning|deprecated/i.test(errorMessage)) return 'medium';
  return 'low';
}

function identifyPossibleCauses(errorMessage: string, stackTrace?: string): string[] {
  const causes: string[] = [];
  
  if (/undefined|null/i.test(errorMessage)) {
    causes.push('Variable not initialized or null reference');
  }
  if (/syntax/i.test(errorMessage)) {
    causes.push('Syntax error in code');
  }
  if (/import|module/i.test(errorMessage)) {
    causes.push('Missing dependency or incorrect import path');
  }
  if (/type/i.test(errorMessage)) {
    causes.push('Type mismatch or incorrect data type usage');
  }
  
  return causes;
}

function generateFixSuggestions(errorMessage: string, stackTrace?: string): string[] {
  const suggestions: string[] = [];
  
  if (/undefined|null/i.test(errorMessage)) {
    suggestions.push('Add null checks before using variables');
    suggestions.push('Initialize variables with default values');
  }
  if (/syntax/i.test(errorMessage)) {
    suggestions.push('Check for missing brackets, semicolons, or quotes');
    suggestions.push('Verify proper indentation and structure');
  }
  if (/import|module/i.test(errorMessage)) {
    suggestions.push('Install missing dependencies');
    suggestions.push('Check import paths and module names');
  }
  
  return suggestions;
}

function generateDebuggingSteps(errorMessage: string): string[] {
  return [
    'Reproduce the error consistently',
    'Check recent code changes',
    'Review error logs and stack trace',
    'Test with minimal example',
    'Verify environment and dependencies'
  ];
}

function buildTestCommand(params: any): string {
  const framework = params.testFramework || 'jest';
  const commands = {
    'jest': `npx jest ${params.testPath || ''} ${params.verbose ? '--verbose' : ''} ${params.coverage ? '--coverage' : ''}`,
    'mocha': `npx mocha ${params.testPath || 'test/**/*.js'} ${params.verbose ? '--reporter spec' : ''}`,
    'vitest': `npx vitest ${params.testPath || ''} ${params.coverage ? '--coverage' : ''}`,
    'pytest': `python -m pytest ${params.testPath || ''} ${params.verbose ? '-v' : ''}`,
    'go-test': `go test ${params.testPath || './...'} ${params.verbose ? '-v' : ''}`,
    'cargo-test': `cargo test ${params.testPath || ''} ${params.verbose ? '--verbose' : ''}`
  };
  
  return commands[framework] || commands.jest;
}

function parseTestOutput(stdout: string, stderr: string, framework?: string): any {
  // Simplified test result parsing
  const passed = !stderr && !/failed|error/i.test(stdout);
  const failedCount = (stdout.match(/failed/gi) || []).length;
  
  return {
    passed,
    failed: failedCount,
    total: failedCount + (passed ? 1 : 0),
    output: stdout,
    errors: stderr
  };
}

function detectLanguageFromPath(filePath: string): string {
  const ext = filePath.split('.').pop()?.toLowerCase();
  const languageMap: Record<string, string> = {
    'js': 'javascript',
    'ts': 'typescript',
    'py': 'python',
    'java': 'java',
    'cpp': 'cpp',
    'c': 'c',
    'go': 'go',
    'rs': 'rust'
  };
  return languageMap[ext || ''] || 'text';
}

function buildSyntaxCheckCommand(filePath: string, language: string): string | null {
  const commands: Record<string, string> = {
    'javascript': `node --check ${filePath}`,
    'typescript': `npx tsc --noEmit ${filePath}`,
    'python': `python -m py_compile ${filePath}`,
    'go': `go build -o /dev/null ${filePath}`,
    'rust': `rustc --emit=metadata ${filePath}`
  };
  
  return commands[language] || null;
}

function parseSyntaxOutput(stdout: string, stderr: string, language: string): any[] {
  const issues: any[] = [];
  
  if (stderr) {
    const lines = stderr.split('\n');
    for (const line of lines) {
      if (line.trim()) {
        issues.push({
          message: line,
          severity: /error/i.test(line) ? 'error' : 'warning',
          line: extractLineNumber(line)
        });
      }
    }
  }
  
  return issues;
}

function extractLineNumber(message: string): number | null {
  const match = message.match(/:(\d+):/);
  return match ? parseInt(match[1]) : null;
}

async function generateIntelligentFix(params: any): Promise<any[]> {
  // Placeholder for AI-powered fix generation
  return [
    {
      description: `Fix for: ${params.errorMessage}`,
      changes: [
        {
          file: params.filePath,
          line: params.lineNumber,
          action: 'replace',
          content: '// AI-generated fix would go here'
        }
      ],
      confidence: 0.8
    }
  ];
}

async function monitorProcess(params: any): Promise<any> {
  // Simplified process monitoring
  return {
    processName: params.processName,
    pid: params.pid,
    duration: params.duration,
    metrics: {
      cpu: Math.random() * 100,
      memory: Math.random() * 1000,
      status: 'running'
    }
  };
}

// Export all tools
export default [
  analyzeErrorTool,
  runTestsTool,
  checkSyntaxTool,
  generateFixTool,
  monitorProcessTool
];
