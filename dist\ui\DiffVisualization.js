"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DiffVisualization = void 0;
const chalk_1 = __importDefault(require("chalk"));
const strip_ansi_1 = __importDefault(require("strip-ansi"));
class DiffVisualization {
    static DEFAULT_OPTIONS = {
        maxLines: 50,
        showLineNumbers: true,
        showStats: true,
        colorize: true,
        contextLines: 3,
        showHeader: true,
        compact: false
    };
    /**
     * Render a diff for terminal display
     */
    static renderDiff(diff, filePath, options = {}) {
        const opts = { ...this.DEFAULT_OPTIONS, ...options };
        const lines = [];
        if (opts.showHeader) {
            lines.push(this.renderHeader(filePath, diff, opts));
        }
        if (opts.showStats) {
            lines.push(this.renderStats(diff.stats, opts));
        }
        // Render hunks
        let totalLines = 0;
        for (const hunk of diff.hunks) {
            if (opts.maxLines && totalLines >= opts.maxLines) {
                lines.push(this.colorize('... (truncated)', 'gray', opts));
                break;
            }
            const hunkLines = this.renderHunk(hunk, opts);
            lines.push(...hunkLines);
            totalLines += hunkLines.length;
        }
        return lines.join('\n');
    }
    /**
     * Render diff header
     */
    static renderHeader(filePath, diff, options) {
        const header = `diff --git a/${filePath} b/${filePath}`;
        return options.colorize ? chalk_1.default.bold(header) : header;
    }
    /**
     * Render diff statistics
     */
    static renderStats(stats, options) {
        const { additions, deletions, changes } = stats;
        const parts = [];
        if (additions > 0) {
            const addText = `+${additions}`;
            parts.push(options.colorize ? chalk_1.default.green(addText) : addText);
        }
        if (deletions > 0) {
            const delText = `-${deletions}`;
            parts.push(options.colorize ? chalk_1.default.red(delText) : delText);
        }
        if (changes > 0) {
            const changeText = `~${changes}`;
            parts.push(options.colorize ? chalk_1.default.yellow(changeText) : changeText);
        }
        const statsLine = parts.length > 0 ? `(${parts.join(' ')})` : '(no changes)';
        return options.colorize ? chalk_1.default.dim(statsLine) : statsLine;
    }
    /**
     * Render a single diff hunk
     */
    static renderHunk(hunk, options) {
        const lines = [];
        // Hunk header
        const hunkHeader = `@@ -${hunk.oldStart},${hunk.oldLines} +${hunk.newStart},${hunk.newLines} @@`;
        lines.push(this.colorize(hunkHeader, 'cyan', options));
        // Hunk lines
        let oldLineNum = hunk.oldStart;
        let newLineNum = hunk.newStart;
        for (const line of hunk.lines) {
            const renderedLine = this.renderDiffLine(line, oldLineNum, newLineNum, options);
            lines.push(renderedLine);
            // Update line numbers
            if (line.type === 'remove' || line.type === 'context') {
                oldLineNum++;
            }
            if (line.type === 'add' || line.type === 'context') {
                newLineNum++;
            }
        }
        return lines;
    }
    /**
     * Render a single diff line
     */
    static renderDiffLine(line, oldLineNum, newLineNum, options) {
        let prefix = ' ';
        let color = 'white';
        let lineNumStr = '';
        // Determine prefix and color
        switch (line.type) {
            case 'add':
                prefix = '+';
                color = 'green';
                break;
            case 'remove':
                prefix = '-';
                color = 'red';
                break;
            case 'context':
                prefix = ' ';
                color = 'white';
                break;
        }
        // Build line number display
        if (options.showLineNumbers) {
            const oldNum = line.type === 'add' ? '   ' : oldLineNum.toString().padStart(3);
            const newNum = line.type === 'remove' ? '   ' : newLineNum.toString().padStart(3);
            lineNumStr = options.colorize
                ? chalk_1.default.dim(`${oldNum} ${newNum} `)
                : `${oldNum} ${newNum} `;
        }
        // Build content
        const content = `${prefix}${line.content}`;
        const colorizedContent = this.colorize(content, color, options);
        return `${lineNumStr}${colorizedContent}`;
    }
    /**
     * Render multiple changes as a summary
     */
    static renderChangesSummary(changes, options = {}) {
        const opts = { ...this.DEFAULT_OPTIONS, ...options };
        const lines = [];
        if (changes.length === 0) {
            return this.colorize('No changes tracked', 'gray', opts);
        }
        lines.push(this.colorize(`\n📋 Change Summary (${changes.length} changes)`, 'bold', opts));
        lines.push(this.colorize('─'.repeat(50), 'gray', opts));
        for (const change of changes.slice(-10)) { // Show last 10 changes
            const timeStr = change.timestamp.toLocaleTimeString();
            const typeIcon = this.getChangeTypeIcon(change.type);
            const pathStr = change.path.length > 40
                ? '...' + change.path.slice(-37)
                : change.path;
            let line = `${typeIcon} ${pathStr}`;
            if (change.diff) {
                const stats = this.renderStats(change.diff.stats, opts);
                line += ` ${stats}`;
            }
            line += this.colorize(` (${timeStr})`, 'gray', opts);
            lines.push(line);
        }
        return lines.join('\n');
    }
    /**
     * Render side-by-side diff comparison
     */
    static renderSideBySide(diff, filePath, options = {}) {
        const opts = { ...this.DEFAULT_OPTIONS, ...options };
        const lines = [];
        const terminalWidth = process.stdout.columns || 120;
        const halfWidth = Math.floor((terminalWidth - 10) / 2);
        if (opts.showHeader) {
            lines.push(this.renderHeader(filePath, diff, opts));
            lines.push(this.colorize('─'.repeat(terminalWidth), 'gray', opts));
        }
        // Split content into old and new
        const oldLines = diff.oldContent?.split('\n') || [];
        const newLines = diff.newContent?.split('\n') || [];
        const maxLines = Math.max(oldLines.length, newLines.length);
        // Header for side-by-side
        const oldHeader = 'Original'.padEnd(halfWidth);
        const newHeader = 'Modified'.padEnd(halfWidth);
        lines.push(this.colorize(oldHeader, 'red', opts) +
            ' │ ' +
            this.colorize(newHeader, 'green', opts));
        lines.push(this.colorize('─'.repeat(terminalWidth), 'gray', opts));
        // Render lines side by side
        for (let i = 0; i < Math.min(maxLines, opts.maxLines || 50); i++) {
            const oldLine = (oldLines[i] || '').substring(0, halfWidth - 3).padEnd(halfWidth);
            const newLine = (newLines[i] || '').substring(0, halfWidth - 3).padEnd(halfWidth);
            const oldColored = oldLines[i] !== newLines[i]
                ? this.colorize(oldLine, 'red', opts)
                : oldLine;
            const newColored = oldLines[i] !== newLines[i]
                ? this.colorize(newLine, 'green', opts)
                : newLine;
            lines.push(`${oldColored} │ ${newColored}`);
        }
        if (maxLines > (opts.maxLines || 50)) {
            lines.push(this.colorize('... (truncated)', 'gray', opts));
        }
        return lines.join('\n');
    }
    /**
     * Render compact diff (one-line summary)
     */
    static renderCompact(diff, filePath, options = {}) {
        const opts = { ...this.DEFAULT_OPTIONS, ...options };
        const stats = this.renderStats(diff.stats, opts);
        const fileName = filePath.split('/').pop() || filePath;
        return `${this.getChangeTypeIcon('file-modify')} ${fileName} ${stats}`;
    }
    /**
     * Get icon for change type
     */
    static getChangeTypeIcon(type) {
        const icons = {
            'file-create': '📄',
            'file-modify': '✏️',
            'file-delete': '🗑️',
            'git-operation': '🔀'
        };
        return icons[type] || '📝';
    }
    /**
     * Apply color if colorization is enabled
     */
    static colorize(text, color, options) {
        if (!options.colorize) {
            return text;
        }
        switch (color) {
            case 'red': return chalk_1.default.red(text);
            case 'green': return chalk_1.default.green(text);
            case 'yellow': return chalk_1.default.yellow(text);
            case 'blue': return chalk_1.default.blue(text);
            case 'cyan': return chalk_1.default.cyan(text);
            case 'magenta': return chalk_1.default.magenta(text);
            case 'gray': return chalk_1.default.gray(text);
            case 'dim': return chalk_1.default.dim(text);
            case 'bold': return chalk_1.default.bold(text);
            default: return text;
        }
    }
    /**
     * Create a diff visualization for terminal display
     */
    static createTerminalDiff(oldContent, newContent, filePath, options = {}) {
        // This would integrate with the DiffTracker to generate the actual diff
        const mockDiff = {
            oldContent,
            newContent,
            hunks: [], // Would be populated by actual diff algorithm
            stats: {
                additions: newContent.split('\n').length - oldContent.split('\n').length,
                deletions: 0,
                changes: 1
            }
        };
        return this.renderDiff(mockDiff, filePath, options);
    }
    /**
     * Format diff for streaming output
     */
    static formatForStreaming(diff, filePath) {
        return this.renderCompact(diff, filePath, {
            colorize: true,
            showStats: true
        });
    }
    /**
     * Get terminal width for responsive rendering
     */
    static getTerminalWidth() {
        return process.stdout.columns || 120;
    }
    /**
     * Strip ANSI colors for plain text output
     */
    static stripColors(text) {
        return (0, strip_ansi_1.default)(text);
    }
}
exports.DiffVisualization = DiffVisualization;
//# sourceMappingURL=DiffVisualization.js.map