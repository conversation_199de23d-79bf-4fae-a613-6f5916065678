"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.exportContextTool = exports.getSessionStatsTool = exports.clearHistoryTool = exports.getContextTool = void 0;
const zod_1 = require("zod");
const nanoid_1 = require("nanoid");
// Get Context Tool
const getContextSchema = zod_1.z.object({
    includeHistory: zod_1.z.boolean().default(true).optional().describe('Include conversation history'),
    includeChanges: zod_1.z.boolean().default(true).optional().describe('Include change history'),
    limit: zod_1.z.number().default(10).optional().describe('Limit for history items')
});
exports.getContextTool = {
    name: 'get_context',
    description: 'Get current agent context and session information',
    schema: getContextSchema,
    category: 'context-management',
    async execute(params, context) {
        try {
            const contextInfo = {
                sessionId: context.sessionId,
                workingDirectory: context.workingDirectory,
                activeTools: context.activeTools,
                gitRepository: context.gitRepository ? {
                    currentBranch: context.gitRepository.currentBranch,
                    hasRemotes: context.gitRepository.remotes.length > 0,
                    hasChanges: context.gitRepository.status.modified.length > 0 ||
                        context.gitRepository.status.added.length > 0 ||
                        context.gitRepository.status.deleted.length > 0
                } : null
            };
            if (params.includeHistory) {
                contextInfo.conversationHistory = context.conversationHistory
                    .slice(-params.limit)
                    .map(msg => ({
                    id: msg.id,
                    role: msg.role,
                    content: msg.content.substring(0, 200) + (msg.content.length > 200 ? '...' : ''),
                    timestamp: msg.timestamp,
                    hasToolCalls: !!(msg.toolCalls && msg.toolCalls.length > 0)
                }));
            }
            if (params.includeChanges) {
                contextInfo.recentChanges = context.changeHistory
                    .slice(-params.limit)
                    .map(change => ({
                    id: change.id,
                    type: change.type,
                    path: change.path,
                    timestamp: change.timestamp,
                    hasDiff: !!change.diff
                }));
            }
            return {
                id: (0, nanoid_1.nanoid)(),
                toolCallId: (0, nanoid_1.nanoid)(),
                success: true,
                output: contextInfo,
                timestamp: new Date(),
                metadata: {
                    operation: 'get-context',
                    includeHistory: params.includeHistory,
                    includeChanges: params.includeChanges
                }
            };
        }
        catch (error) {
            return {
                id: (0, nanoid_1.nanoid)(),
                toolCallId: (0, nanoid_1.nanoid)(),
                success: false,
                output: null,
                error: `Failed to get context: ${error.message}`,
                timestamp: new Date()
            };
        }
    }
};
// Clear History Tool
const clearHistorySchema = zod_1.z.object({
    type: zod_1.z.enum(['conversation', 'changes', 'all']).describe('Type of history to clear'),
    confirm: zod_1.z.boolean().default(false).describe('Confirmation flag to prevent accidental clearing')
});
exports.clearHistoryTool = {
    name: 'clear_history',
    description: 'Clear conversation or change history',
    schema: clearHistorySchema,
    category: 'context-management',
    async execute(params, context) {
        try {
            if (!params.confirm) {
                return {
                    id: (0, nanoid_1.nanoid)(),
                    toolCallId: (0, nanoid_1.nanoid)(),
                    success: false,
                    output: null,
                    error: 'Confirmation required to clear history. Set confirm: true',
                    timestamp: new Date()
                };
            }
            let cleared = [];
            switch (params.type) {
                case 'conversation':
                    context.conversationHistory = [];
                    cleared.push('conversation history');
                    break;
                case 'changes':
                    context.changeHistory = [];
                    cleared.push('change history');
                    break;
                case 'all':
                    context.conversationHistory = [];
                    context.changeHistory = [];
                    cleared.push('conversation history', 'change history');
                    break;
            }
            return {
                id: (0, nanoid_1.nanoid)(),
                toolCallId: (0, nanoid_1.nanoid)(),
                success: true,
                output: {
                    cleared,
                    type: params.type
                },
                timestamp: new Date(),
                metadata: {
                    operation: 'clear-history',
                    type: params.type
                }
            };
        }
        catch (error) {
            return {
                id: (0, nanoid_1.nanoid)(),
                toolCallId: (0, nanoid_1.nanoid)(),
                success: false,
                output: null,
                error: `Failed to clear history: ${error.message}`,
                timestamp: new Date()
            };
        }
    }
};
// Get Session Stats Tool
const getSessionStatsSchema = zod_1.z.object({
    detailed: zod_1.z.boolean().default(false).optional().describe('Include detailed statistics')
});
exports.getSessionStatsTool = {
    name: 'get_session_stats',
    description: 'Get statistics about the current session',
    schema: getSessionStatsSchema,
    category: 'context-management',
    async execute(params, context) {
        try {
            const now = new Date();
            const recentChanges = context.changeHistory.filter(change => now.getTime() - change.timestamp.getTime() < 3600000 // Last hour
            );
            const userMessages = context.conversationHistory.filter(m => m.role === 'user');
            const assistantMessages = context.conversationHistory.filter(m => m.role === 'assistant');
            const toolCalls = context.conversationHistory.reduce((count, msg) => count + (msg.toolCalls?.length || 0), 0);
            const stats = {
                sessionId: context.sessionId,
                workingDirectory: context.workingDirectory,
                conversation: {
                    totalMessages: context.conversationHistory.length,
                    userMessages: userMessages.length,
                    assistantMessages: assistantMessages.length,
                    toolCalls
                },
                changes: {
                    totalChanges: context.changeHistory.length,
                    recentChanges: recentChanges.length,
                    fileOperations: context.changeHistory.filter(c => c.type.startsWith('file-')).length,
                    gitOperations: context.changeHistory.filter(c => c.type === 'git-operation').length
                },
                tools: {
                    activeTools: context.activeTools.length,
                    availableCategories: [...new Set(context.activeTools.map(tool => {
                            // This is a simplified categorization
                            if (tool.includes('file'))
                                return 'file-operations';
                            if (tool.includes('git'))
                                return 'git-operations';
                            if (tool.includes('execute') || tool.includes('command'))
                                return 'shell-commands';
                            return 'other';
                        }))]
                }
            };
            if (params.detailed) {
                stats['detailed'] = {
                    gitRepository: context.gitRepository,
                    semanticIndex: context.semanticIndex ? {
                        hasIndex: true,
                        lastUpdated: context.semanticIndex.lastUpdated,
                        version: context.semanticIndex.version
                    } : { hasIndex: false },
                    changeTypes: context.changeHistory.reduce((acc, change) => {
                        acc[change.type] = (acc[change.type] || 0) + 1;
                        return acc;
                    }, {})
                };
            }
            return {
                id: (0, nanoid_1.nanoid)(),
                toolCallId: (0, nanoid_1.nanoid)(),
                success: true,
                output: stats,
                timestamp: new Date(),
                metadata: {
                    operation: 'session-stats',
                    detailed: params.detailed
                }
            };
        }
        catch (error) {
            return {
                id: (0, nanoid_1.nanoid)(),
                toolCallId: (0, nanoid_1.nanoid)(),
                success: false,
                output: null,
                error: `Failed to get session stats: ${error.message}`,
                timestamp: new Date()
            };
        }
    }
};
// Export Context Tool
const exportContextSchema = zod_1.z.object({
    filePath: zod_1.z.string().describe('Path where to export the context'),
    includeHistory: zod_1.z.boolean().default(true).optional().describe('Include conversation history'),
    includeChanges: zod_1.z.boolean().default(true).optional().describe('Include change history')
});
exports.exportContextTool = {
    name: 'export_context',
    description: 'Export current context to a file for backup or analysis',
    schema: exportContextSchema,
    category: 'context-management',
    async execute(params, context) {
        try {
            const exportData = {
                sessionId: context.sessionId,
                workingDirectory: context.workingDirectory,
                activeTools: context.activeTools,
                gitRepository: context.gitRepository,
                exportedAt: new Date(),
                version: '1.0.0'
            };
            if (params.includeHistory) {
                exportData.conversationHistory = context.conversationHistory;
            }
            if (params.includeChanges) {
                exportData.changeHistory = context.changeHistory;
            }
            // In a real implementation, this would write to the file system
            // For now, we'll just return the data structure
            return {
                id: (0, nanoid_1.nanoid)(),
                toolCallId: (0, nanoid_1.nanoid)(),
                success: true,
                output: {
                    filePath: params.filePath,
                    exported: true,
                    dataSize: JSON.stringify(exportData).length,
                    includeHistory: params.includeHistory,
                    includeChanges: params.includeChanges
                },
                timestamp: new Date(),
                metadata: {
                    operation: 'export-context',
                    filePath: params.filePath,
                    requiresFollowup: true // Would need file write operation
                }
            };
        }
        catch (error) {
            return {
                id: (0, nanoid_1.nanoid)(),
                toolCallId: (0, nanoid_1.nanoid)(),
                success: false,
                output: null,
                error: `Failed to export context: ${error.message}`,
                timestamp: new Date()
            };
        }
    }
};
exports.default = [
    exports.getContextTool,
    exports.clearHistoryTool,
    exports.getSessionStatsTool,
    exports.exportContextTool
];
//# sourceMappingURL=ContextManagement.js.map