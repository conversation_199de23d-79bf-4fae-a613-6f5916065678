{"version": 3, "file": "AIProvider.js", "sourceRoot": "", "sources": ["../../../src/core/ai/AIProvider.ts"], "names": [], "mappings": ";;;;;;AAAA,oDAA4B;AAoB5B;;;GAGG;AACH,MAAa,UAAU;IACb,MAAM,CAAS;IACf,MAAM,CAAc;IAE5B,YAAY,MAAmB;QAC7B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAEO,gBAAgB;QACtB,MAAM,YAAY,GAAQ;YACxB,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM;SAC3B,CAAC;QAEF,wCAAwC;QACxC,QAAQ,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;YAC7B,KAAK,QAAQ;gBACX,+BAA+B;gBAC/B,MAAM;YAER,KAAK,UAAU;gBACb,YAAY,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,6BAA6B,CAAC;gBAC5E,MAAM;YAER,KAAK,QAAQ;gBACX,YAAY,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,2BAA2B,CAAC;gBAC1E,YAAY,CAAC,MAAM,GAAG,QAAQ,CAAC,CAAC,sCAAsC;gBACtE,MAAM;YAER;gBACE,MAAM,IAAI,KAAK,CAAC,4BAA4B,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;QACxE,CAAC;QAED,IAAI,CAAC,MAAM,GAAG,IAAI,gBAAM,CAAC,YAAY,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACf,mBAA0C,EAC1C,WAAkB,EAClB,OAAqB;QAErB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAC;YACnE,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;YAElD,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBAC3D,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;gBACxB,QAAQ;gBACR,KAAK,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;gBAC3C,WAAW,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;gBAClD,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,GAAG;gBAC3C,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI;gBACzC,MAAM,EAAE,KAAK,EAAE,uCAAuC;aACvD,CAAC,CAAC;YAEH,OAAO,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QAExC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,sBAAuB,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC;QACpE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CACxB,mBAA0C,EAC1C,WAAkB,EAClB,OAAqB,EACrB,OAAgC,EAChC,UAAwC;QAExC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAC;YACnE,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;YAElD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACvD,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;gBACxB,QAAQ;gBACR,KAAK,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;gBAC3C,WAAW,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;gBAClD,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,GAAG;gBAC3C,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI;gBACzC,MAAM,EAAE,IAAI;aACb,CAAC,CAAC;YAEH,IAAI,OAAO,GAAG,EAAE,CAAC;YACjB,IAAI,SAAS,GAAe,EAAE,CAAC;YAC/B,IAAI,KAAK,GAAQ,SAAS,CAAC;YAE3B,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBACjC,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;gBAEtC,IAAI,KAAK,EAAE,OAAO,EAAE,CAAC;oBACnB,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC;oBACzB,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;gBACzB,CAAC;gBAED,IAAI,KAAK,EAAE,UAAU,EAAE,CAAC;oBACtB,KAAK,MAAM,QAAQ,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;wBACxC,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;4BACtB,MAAM,cAAc,GAAa;gCAC/B,EAAE,EAAE,QAAQ,CAAC,EAAE,IAAI,QAAQ,IAAI,CAAC,GAAG,EAAE,EAAE;gCACvC,IAAI,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI;gCAC5B,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,IAAI,IAAI,CAAC;gCAC3D,SAAS,EAAE,IAAI,IAAI,EAAE;6BACtB,CAAC;4BACF,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;4BAC/B,UAAU,CAAC,cAAc,CAAC,CAAC;wBAC7B,CAAC;oBACH,CAAC;gBACH,CAAC;gBAED,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;oBAChB,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;gBACtB,CAAC;YACH,CAAC;YAED,OAAO;gBACL,OAAO;gBACP,SAAS,EAAE,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS;gBACvD,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;oBACb,YAAY,EAAE,KAAK,CAAC,aAAa;oBACjC,gBAAgB,EAAE,KAAK,CAAC,iBAAiB;oBACzC,WAAW,EAAE,KAAK,CAAC,YAAY;iBAChC,CAAC,CAAC,CAAC,SAAS;aACd,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,uBAAwB,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,cAAc,CACpB,mBAA0C,EAC1C,OAAqB;QAErB,MAAM,aAAa,GAA6D;YAC9E,IAAI,EAAE,QAAQ;YACd,OAAO,EAAE,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC;SACzC,CAAC;QAEF,MAAM,QAAQ,GAAyD,CAAC,aAAa,CAAC,CAAC;QAEvF,KAAK,MAAM,GAAG,IAAI,mBAAmB,EAAE,CAAC;YACtC,IAAI,GAAG,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;gBACxB,QAAQ,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,GAAG,CAAC,OAAO;iBACrB,CAAC,CAAC;YACL,CAAC;iBAAM,IAAI,GAAG,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;gBACpC,MAAM,gBAAgB,GAAgE;oBACpF,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,GAAG,CAAC,OAAO;iBACrB,CAAC;gBAEF,IAAI,GAAG,CAAC,SAAS,IAAI,GAAG,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC9C,gBAAgB,CAAC,UAAU,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBACrD,EAAE,EAAE,EAAE,CAAC,EAAE;wBACT,IAAI,EAAE,UAAU;wBAChB,QAAQ,EAAE;4BACR,IAAI,EAAE,EAAE,CAAC,IAAI;4BACb,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,UAAU,CAAC;yBACzC;qBACF,CAAC,CAAC,CAAC;gBACN,CAAC;gBAED,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAEhC,oCAAoC;gBACpC,IAAI,GAAG,CAAC,WAAW,IAAI,GAAG,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAClD,KAAK,MAAM,MAAM,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC;wBACrC,QAAQ,CAAC,IAAI,CAAC;4BACZ,IAAI,EAAE,MAAM;4BACZ,YAAY,EAAE,MAAM,CAAC,UAAU;4BAC/B,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC;gCACtB,OAAO,EAAE,MAAM,CAAC,OAAO;gCACvB,MAAM,EAAE,MAAM,CAAC,MAAM;gCACrB,KAAK,EAAE,MAAM,CAAC,KAAK;gCACnB,QAAQ,EAAE,MAAM,CAAC,QAAQ;6BAC1B,CAAC;yBACH,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,OAAqB;QAC7C,OAAO;;;uBAGY,OAAO,CAAC,gBAAgB;gBAC/B,OAAO,CAAC,SAAS;oBACb,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI;kBACtC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;oBAC5B,OAAO,CAAC,aAAa,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;qIAqBqF,CAAC;IACpI,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,WAAkB;QAC1C,OAAO,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAChC,IAAI,EAAE,UAAU;YAChB,QAAQ,EAAE;gBACR,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,WAAW,EAAE,MAAM,CAAC,WAAW;gBAC/B,UAAU,EAAE,MAAM,CAAC,UAAU;aAC9B;SACF,CAAC,CAAC,CAAC;IACN,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,UAAkD;QACtE,MAAM,MAAM,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACrC,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;QAE/B,IAAI,SAAiC,CAAC;QAEtC,IAAI,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxD,SAAS,GAAG,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gBACxC,EAAE,EAAE,EAAE,CAAC,EAAE;gBACT,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI;gBACtB,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC;gBAC7C,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC,CAAC;QACN,CAAC;QAED,OAAO;YACL,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,EAAE;YAC9B,SAAS;YACT,KAAK,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;gBACxB,YAAY,EAAE,UAAU,CAAC,KAAK,CAAC,aAAa;gBAC5C,gBAAgB,EAAE,UAAU,CAAC,KAAK,CAAC,iBAAiB;gBACpD,WAAW,EAAE,UAAU,CAAC,KAAK,CAAC,YAAY;aAC3C,CAAC,CAAC,CAAC,SAAS;SACd,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,SAAsB;QACjC,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;QACxB,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACxC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK;gBACxB,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAC;gBACxD,UAAU,EAAE,EAAE;aACf,CAAC,CAAC;YACH,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;CACF;AAzSD,gCAySC"}