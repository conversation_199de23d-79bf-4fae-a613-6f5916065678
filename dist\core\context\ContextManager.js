"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContextManager = void 0;
const fs_1 = require("fs");
const path_1 = require("path");
const nanoid_1 = require("nanoid");
const GitManager_1 = require("../git/GitManager");
class ContextManager {
    workingDirectory;
    contextFile;
    gitManager;
    constructor(workingDirectory) {
        this.workingDirectory = workingDirectory;
        this.contextFile = (0, path_1.join)(workingDirectory, '.agent-context.json');
        this.gitManager = new GitManager_1.GitManager(workingDirectory);
    }
    /**
     * Initialize a new agent context
     */
    async initializeContext() {
        const sessionId = (0, nanoid_1.nanoid)();
        // Check if we're in a git repository
        let gitRepository;
        try {
            gitRepository = await this.gitManager.getRepositoryInfo();
        }
        catch {
            // Not a git repository, that's okay
        }
        const context = {
            sessionId,
            workingDirectory: this.workingDirectory,
            gitRepository,
            conversationHistory: [],
            activeTools: [
                'read_file',
                'write_file',
                'delete_file',
                'list_directory',
                'search_files',
                'execute_command',
                'interactive_command',
                'get_system_info',
                'check_process'
            ],
            changeHistory: [],
            semanticIndex: undefined
        };
        // Try to load existing context
        try {
            const existingContext = await this.loadContext();
            // Merge with existing context but keep new session ID
            context.conversationHistory = existingContext.conversationHistory || [];
            context.changeHistory = existingContext.changeHistory || [];
            context.semanticIndex = existingContext.semanticIndex;
        }
        catch {
            // No existing context, start fresh
        }
        return context;
    }
    /**
     * Load context from file
     */
    async loadContext() {
        try {
            const contextData = await fs_1.promises.readFile(this.contextFile, 'utf8');
            const context = JSON.parse(contextData);
            // Convert date strings back to Date objects
            if (context.conversationHistory) {
                context.conversationHistory = context.conversationHistory.map((msg) => ({
                    ...msg,
                    timestamp: new Date(msg.timestamp)
                }));
            }
            if (context.changeHistory) {
                context.changeHistory = context.changeHistory.map((change) => ({
                    ...change,
                    timestamp: new Date(change.timestamp)
                }));
            }
            return context;
        }
        catch (error) {
            throw new Error(`Failed to load context: ${error.message}`);
        }
    }
    /**
     * Save context to file
     */
    async saveContext(context) {
        try {
            // Create a serializable version of the context
            const serializableContext = {
                sessionId: context.sessionId,
                workingDirectory: context.workingDirectory,
                gitRepository: context.gitRepository,
                conversationHistory: context.conversationHistory.slice(-50), // Keep last 50 messages
                activeTools: context.activeTools,
                changeHistory: context.changeHistory.slice(-100), // Keep last 100 changes
                semanticIndex: context.semanticIndex ? {
                    lastUpdated: context.semanticIndex.lastUpdated,
                    version: context.semanticIndex.version
                    // Don't save embeddings to file, they're too large
                } : undefined,
                lastSaved: new Date()
            };
            await fs_1.promises.writeFile(this.contextFile, JSON.stringify(serializableContext, null, 2), 'utf8');
        }
        catch (error) {
            throw new Error(`Failed to save context: ${error.message}`);
        }
    }
    /**
     * Update context with new information
     */
    async updateContext(context) {
        // Update git repository info if available
        try {
            context.gitRepository = await this.gitManager.getRepositoryInfo();
        }
        catch {
            // Git info not available
        }
        // Auto-save context periodically
        await this.saveContext(context);
    }
    /**
     * Add conversation message to context
     */
    addConversationMessage(context, message) {
        context.conversationHistory.push(message);
        // Keep conversation history manageable
        if (context.conversationHistory.length > 100) {
            context.conversationHistory = context.conversationHistory.slice(-50);
        }
    }
    /**
     * Add change record to context
     */
    addChangeRecord(context, change) {
        context.changeHistory.push(change);
        // Keep change history manageable
        if (context.changeHistory.length > 200) {
            context.changeHistory = context.changeHistory.slice(-100);
        }
    }
    /**
     * Get recent changes
     */
    getRecentChanges(context, limit = 10) {
        return context.changeHistory.slice(-limit);
    }
    /**
     * Get conversation summary
     */
    getConversationSummary(context) {
        const messageCount = context.conversationHistory.length;
        const userMessages = context.conversationHistory.filter(m => m.role === 'user').length;
        const assistantMessages = context.conversationHistory.filter(m => m.role === 'assistant').length;
        return `${messageCount} total messages (${userMessages} user, ${assistantMessages} assistant)`;
    }
    /**
     * Clear conversation history
     */
    clearConversationHistory(context) {
        context.conversationHistory = [];
    }
    /**
     * Clear change history
     */
    clearChangeHistory(context) {
        context.changeHistory = [];
    }
    /**
     * Get context statistics
     */
    getContextStats(context) {
        const now = new Date();
        const recentChanges = context.changeHistory.filter(change => now.getTime() - change.timestamp.getTime() < 3600000 // Last hour
        );
        return {
            sessionId: context.sessionId,
            workingDirectory: context.workingDirectory,
            hasGitRepo: !!context.gitRepository,
            conversationMessages: context.conversationHistory.length,
            totalChanges: context.changeHistory.length,
            recentChanges: recentChanges.length,
            activeTools: context.activeTools.length,
            hasSemanticIndex: !!context.semanticIndex
        };
    }
    /**
     * Export context for backup
     */
    async exportContext(context, filePath) {
        try {
            const exportData = {
                ...context,
                exportedAt: new Date(),
                version: '1.0.0'
            };
            await fs_1.promises.writeFile(filePath, JSON.stringify(exportData, null, 2), 'utf8');
        }
        catch (error) {
            throw new Error(`Failed to export context: ${error.message}`);
        }
    }
    /**
     * Import context from backup
     */
    async importContext(filePath) {
        try {
            const contextData = await fs_1.promises.readFile(filePath, 'utf8');
            const context = JSON.parse(contextData);
            // Validate and convert dates
            if (context.conversationHistory) {
                context.conversationHistory = context.conversationHistory.map((msg) => ({
                    ...msg,
                    timestamp: new Date(msg.timestamp)
                }));
            }
            if (context.changeHistory) {
                context.changeHistory = context.changeHistory.map((change) => ({
                    ...change,
                    timestamp: new Date(change.timestamp)
                }));
            }
            return context;
        }
        catch (error) {
            throw new Error(`Failed to import context: ${error.message}`);
        }
    }
    /**
     * Check if context file exists
     */
    async contextExists() {
        try {
            await fs_1.promises.access(this.contextFile);
            return true;
        }
        catch {
            return false;
        }
    }
    /**
     * Delete context file
     */
    async deleteContext() {
        try {
            await fs_1.promises.unlink(this.contextFile);
        }
        catch (error) {
            if (error.code !== 'ENOENT') {
                throw new Error(`Failed to delete context: ${error.message}`);
            }
        }
    }
}
exports.ContextManager = ContextManager;
//# sourceMappingURL=ContextManager.js.map