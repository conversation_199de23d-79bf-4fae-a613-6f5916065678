{"version": 3, "file": "cli.js", "sourceRoot": "", "sources": ["../src/cli.ts"], "names": [], "mappings": ";;;;;;AAEA,yCAAoC;AACpC,kDAA0B;AAC1B,8CAAsB;AACtB,wDAAgC;AAChC,mCAAgC;AAChC,oDAAiD;AAEjD,gDAA6C;AAC7C,+DAA4D;AAE5D,6BAA6B;AAC7B,IAAA,eAAM,GAAE,CAAC;AAET,MAAM,OAAO,GAAG,IAAI,mBAAO,EAAE,CAAC;AAE9B,OAAO;KACJ,IAAI,CAAC,OAAO,CAAC;KACb,WAAW,CAAC,gCAAgC,CAAC;KAC7C,OAAO,CAAC,OAAO,CAAC,CAAC;AAEpB,2BAA2B;AAC3B,OAAO;KACJ,OAAO,CAAC,MAAM,CAAC;KACf,WAAW,CAAC,6BAA6B,CAAC;KAC1C,MAAM,CAAC,2BAA2B,EAAE,wCAAwC,EAAE,QAAQ,CAAC;KACvF,MAAM,CAAC,qBAAqB,EAAE,cAAc,CAAC;KAC7C,MAAM,CAAC,qBAAqB,EAAE,0BAA0B,CAAC;KACzD,MAAM,CAAC,sBAAsB,EAAE,2BAA2B,CAAC;KAC3D,MAAM,CAAC,0BAA0B,EAAE,8BAA8B,EAAE,KAAK,CAAC;KACzE,MAAM,CAAC,uBAAuB,EAAE,mBAAmB,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC;KACnE,MAAM,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;IACxB,IAAI,CAAC;QACH,MAAM,aAAa,GAAG,IAAI,6BAAa,EAAE,CAAC;QAC1C,IAAI,WAAwB,CAAC;QAE7B,gDAAgD;QAChD,IAAI,CAAC;YACH,WAAW,GAAG,MAAM,aAAa,CAAC,UAAU,EAAE,CAAC;YAC/C,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC,CAAC;QAC9D,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,MAAM,CAAC,gDAAgD,CAAC,CAAC,CAAC;YAC5E,WAAW,GAAG,MAAM,kBAAkB,CAAC,OAAO,CAAC,CAAC;YAChD,MAAM,aAAa,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;QAC9C,CAAC;QAED,qCAAqC;QACrC,IAAI,OAAO,CAAC,QAAQ;YAAE,WAAW,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAsB,CAAC;QAC5E,IAAI,OAAO,CAAC,KAAK;YAAE,WAAW,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;QACrD,IAAI,OAAO,CAAC,MAAM;YAAE,WAAW,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QACxD,IAAI,OAAO,CAAC,OAAO;YAAE,WAAW,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAC3D,IAAI,OAAO,CAAC,WAAW;YAAE,WAAW,CAAC,WAAW,GAAG,UAAU,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QAEnF,yBAAyB;QACzB,IAAI,CAAC,WAAW,CAAC,MAAM,IAAI,WAAW,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;YAC7D,OAAO,CAAC,KAAK,CAAC,eAAK,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC,CAAC;YACzE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC;QAED,mBAAmB;QACnB,MAAM,OAAO,GAAG,IAAA,aAAG,EAAC,uBAAuB,CAAC,CAAC,KAAK,EAAE,CAAC;QACrD,MAAM,KAAK,GAAG,IAAI,yBAAW,CAAC,WAAW,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;QAE9D,0BAA0B;QAC1B,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC5B,KAAK,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;QAElD,oBAAoB;QACpB,MAAM,UAAU,GAAG,IAAI,uBAAU,CAAC,KAAK,CAAC,CAAC;QACzC,MAAM,UAAU,CAAC,KAAK,EAAE,CAAC;IAE3B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,eAAK,CAAC,GAAG,CAAC,uBAAuB,CAAC,EAAG,KAAe,CAAC,OAAO,CAAC,CAAC;QAC5E,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;AACH,CAAC,CAAC,CAAC;AAEL,6BAA6B;AAC7B,OAAO;KACJ,OAAO,CAAC,cAAc,CAAC;KACvB,WAAW,CAAC,kCAAkC,CAAC;KAC/C,MAAM,CAAC,2BAA2B,EAAE,wCAAwC,EAAE,QAAQ,CAAC;KACvF,MAAM,CAAC,qBAAqB,EAAE,cAAc,CAAC;KAC7C,MAAM,CAAC,qBAAqB,EAAE,0BAA0B,CAAC;KACzD,MAAM,CAAC,sBAAsB,EAAE,2BAA2B,CAAC;KAC3D,MAAM,CAAC,uBAAuB,EAAE,mBAAmB,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC;KACnE,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE;IAChC,IAAI,CAAC;QACH,MAAM,aAAa,GAAG,IAAI,6BAAa,EAAE,CAAC;QAC1C,IAAI,WAAwB,CAAC;QAE7B,IAAI,CAAC;YACH,WAAW,GAAG,MAAM,aAAa,CAAC,UAAU,EAAE,CAAC;QACjD,CAAC;QAAC,MAAM,CAAC;YACP,WAAW,GAAG,MAAM,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAClD,CAAC;QAED,qCAAqC;QACrC,IAAI,OAAO,CAAC,QAAQ;YAAE,WAAW,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAsB,CAAC;QAC5E,IAAI,OAAO,CAAC,KAAK;YAAE,WAAW,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;QACrD,IAAI,OAAO,CAAC,MAAM;YAAE,WAAW,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QACxD,IAAI,OAAO,CAAC,OAAO;YAAE,WAAW,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAE3D,qBAAqB;QACrB,MAAM,OAAO,GAAG,IAAA,aAAG,EAAC,uBAAuB,CAAC,CAAC,KAAK,EAAE,CAAC;QACrD,MAAM,KAAK,GAAG,IAAI,yBAAW,CAAC,WAAW,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;QAE9D,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC5B,KAAK,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,mCAAmC;QACnC,KAAK,CAAC,EAAE,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAClC,OAAO,CAAC,IAAI,GAAG,qBAAqB,CAAC;QACvC,CAAC,CAAC,CAAC;QAEH,KAAK,CAAC,EAAE,CAAC,wBAAwB,EAAE,CAAC,QAAQ,EAAE,EAAE;YAC9C,OAAO,CAAC,IAAI,GAAG,cAAc,QAAQ,CAAC,IAAI,EAAE,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,KAAK,CAAC,EAAE,CAAC,sBAAsB,EAAE,GAAG,EAAE;YACpC,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,qBAAqB;QACrB,MAAM,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;IAEnC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,eAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAG,KAAe,CAAC,OAAO,CAAC,CAAC;QAC7D,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;AACH,CAAC,CAAC,CAAC;AAEL,oCAAoC;AACpC,OAAO;KACJ,OAAO,CAAC,QAAQ,CAAC;KACjB,WAAW,CAAC,4BAA4B,CAAC;KACzC,MAAM,CAAC,QAAQ,EAAE,4BAA4B,CAAC;KAC9C,MAAM,CAAC,SAAS,EAAE,qBAAqB,CAAC;KACxC,MAAM,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;IACxB,MAAM,aAAa,GAAG,IAAI,6BAAa,EAAE,CAAC;IAE1C,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;QACjB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,aAAa,CAAC,UAAU,EAAE,CAAC;YAChD,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,CAAC;YAClD,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC;gBACzB,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,KAAK,EAAE,MAAM,CAAC,KAAK;gBACnB,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,WAAW,EAAE,MAAM,CAAC,WAAW;gBAC/B,SAAS,EAAE,MAAM,CAAC,SAAS;aAC5B,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;QACf,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,MAAM,CAAC,wBAAwB,CAAC,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;SAAM,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;QACzB,MAAM,aAAa,CAAC,WAAW,EAAE,CAAC;QAClC,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC,CAAC;IAC/D,CAAC;SAAM,CAAC;QACN,MAAM,MAAM,GAAG,MAAM,kBAAkB,CAAC,EAAE,CAAC,CAAC;QAC5C,MAAM,aAAa,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QACvC,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAC,CAAC;IAC/D,CAAC;AACH,CAAC,CAAC,CAAC;AAEL,oCAAoC;AACpC,KAAK,UAAU,kBAAkB,CAAC,OAAY;IAC5C,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC,CAAC;IAE5D,MAAM,SAAS,GAAG;QAChB;YACE,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,UAAU;YAChB,OAAO,EAAE,qBAAqB;YAC9B,OAAO,EAAE;gBACP,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE;gBACnC,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU,EAAE;gBACvC,EAAE,IAAI,EAAE,gBAAgB,EAAE,KAAK,EAAE,QAAQ,EAAE;aAC5C;YACD,OAAO,EAAE,OAAO,CAAC,QAAQ,IAAI,QAAQ;SACtC;QACD;YACE,IAAI,EAAE,OAAO;YACb,IAAI,EAAE,QAAQ;YACd,OAAO,EAAE,gBAAgB;YACzB,IAAI,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,QAAQ,KAAK,QAAQ;YAChD,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,qBAAqB;SAC/D;QACD;YACE,IAAI,EAAE,OAAO;YACb,IAAI,EAAE,SAAS;YACf,OAAO,EAAE,4BAA4B;YACrC,IAAI,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,QAAQ,KAAK,QAAQ;SACjD;QACD;YACE,IAAI,EAAE,OAAO;YACb,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,mBAAmB;YAC5B,OAAO,EAAE,CAAC,OAAO,EAAE,EAAE;gBACnB,QAAQ,OAAO,CAAC,QAAQ,EAAE,CAAC;oBACzB,KAAK,QAAQ,CAAC,CAAC,OAAO,qBAAqB,CAAC;oBAC5C,KAAK,UAAU,CAAC,CAAC,OAAO,eAAe,CAAC;oBACxC,KAAK,QAAQ,CAAC,CAAC,OAAO,QAAQ,CAAC;oBAC/B,OAAO,CAAC,CAAC,OAAO,qBAAqB,CAAC;gBACxC,CAAC;YACH,CAAC;SACF;QACD;YACE,IAAI,EAAE,QAAQ;YACd,IAAI,EAAE,aAAa;YACnB,OAAO,EAAE,wBAAwB;YACjC,OAAO,EAAE,GAAG;YACZ,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;SAC9C;QACD;YACE,IAAI,EAAE,QAAQ;YACd,IAAI,EAAE,WAAW;YACjB,OAAO,EAAE,wBAAwB;YACjC,OAAO,EAAE,IAAI;SACd;KACF,CAAC;IAEF,MAAM,OAAO,GAAG,MAAM,kBAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IAEjD,OAAO;QACL,QAAQ,EAAE,OAAO,CAAC,QAAQ;QAC1B,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,QAAQ;QAClC,OAAO,EAAE,OAAO,CAAC,OAAO;QACxB,KAAK,EAAE,OAAO,CAAC,KAAK;QACpB,WAAW,EAAE,OAAO,CAAC,WAAW;QAChC,SAAS,EAAE,OAAO,CAAC,SAAS;KAC7B,CAAC;AACJ,CAAC;AAED,iBAAiB;AACjB,OAAO,CAAC,EAAE,CAAC,mBAAmB,EAAE,CAAC,KAAK,EAAE,EAAE;IACxC,OAAO,CAAC,KAAK,CAAC,eAAK,CAAC,GAAG,CAAC,qBAAqB,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;IAC/D,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC;AAEH,OAAO,CAAC,EAAE,CAAC,oBAAoB,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE;IACnD,OAAO,CAAC,KAAK,CAAC,eAAK,CAAC,GAAG,CAAC,yBAAyB,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;IAChF,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC;AAEH,oBAAoB;AACpB,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;IACxB,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,MAAM,CAAC,+BAA+B,CAAC,CAAC,CAAC;IAC3D,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC;AAEH,OAAO,CAAC,KAAK,EAAE,CAAC"}