"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolRegistry = void 0;
const events_1 = require("events");
const nanoid_1 = require("nanoid");
const types_1 = require("../../types");
const zod_1 = require("zod");
/**
 * Registry of available tools and their schemas
 * Manages tool registration, validation, and execution
 */
class ToolRegistry extends events_1.EventEmitter {
    tools = new Map();
    toolCategories = new Map();
    executionHistory = [];
    constructor() {
        super();
        this.initializeCategories();
    }
    initializeCategories() {
        const categories = [
            'file-operations',
            'shell-commands',
            'git-operations',
            'semantic-search',
            'debugging',
            'context-management'
        ];
        categories.forEach(category => {
            this.toolCategories.set(category, []);
        });
    }
    /**
     * Register a new tool
     */
    async registerTool(toolModule) {
        try {
            const tool = toolModule.default || toolModule;
            // Validate tool structure
            this.validateTool(tool);
            // Register the tool
            this.tools.set(tool.name, tool);
            // Add to category
            const categoryTools = this.toolCategories.get(tool.category) || [];
            categoryTools.push(tool.name);
            this.toolCategories.set(tool.category, categoryTools);
            this.emit('tool-registered', { name: tool.name, category: tool.category });
        }
        catch (error) {
            throw new types_1.AgentError(`Failed to register tool: ${error.message}`, 'tool-registration-failed', 'system', false);
        }
    }
    /**
     * Execute a tool with given parameters
     */
    async executeTool(toolName, parameters, context) {
        const startTime = Date.now();
        try {
            const tool = this.tools.get(toolName);
            if (!tool) {
                throw new Error(`Tool '${toolName}' not found`);
            }
            // Validate parameters against tool schema
            const validatedParams = tool.schema.parse(parameters);
            this.emit('tool-execution-started', {
                toolName,
                parameters: validatedParams,
                context
            });
            // Execute the tool
            const result = await tool.execute(validatedParams, context);
            // Add execution metadata
            const finalResult = {
                ...result,
                id: result.id || (0, nanoid_1.nanoid)(),
                metadata: {
                    ...result.metadata,
                    executionTime: Date.now() - startTime,
                    toolName,
                    category: tool.category
                }
            };
            // Store in execution history
            this.executionHistory.push(finalResult);
            this.emit('tool-executed', finalResult);
            return finalResult;
        }
        catch (error) {
            const errorResult = {
                id: (0, nanoid_1.nanoid)(),
                toolCallId: `error_${(0, nanoid_1.nanoid)()}`,
                success: false,
                output: null,
                error: error.message,
                timestamp: new Date(),
                metadata: {
                    executionTime: Date.now() - startTime,
                    toolName,
                    errorType: error.constructor.name
                }
            };
            this.executionHistory.push(errorResult);
            this.emit('tool-execution-failed', errorResult);
            return errorResult;
        }
    }
    /**
     * Execute multiple tools in parallel
     */
    async executeToolsParallel(toolCalls, context) {
        const promises = toolCalls.map(toolCall => this.executeTool(toolCall.name, toolCall.parameters, context));
        return Promise.all(promises);
    }
    /**
     * Execute multiple tools in sequence
     */
    async executeToolsSequence(toolCalls, context) {
        const results = [];
        for (const toolCall of toolCalls) {
            const result = await this.executeTool(toolCall.name, toolCall.parameters, context);
            results.push(result);
            // Stop execution if a critical tool fails
            if (!result.success && result.metadata?.critical) {
                break;
            }
        }
        return results;
    }
    /**
     * Get tool schemas for AI provider
     */
    getToolSchemas() {
        return Array.from(this.tools.values()).map(tool => ({
            name: tool.name,
            description: tool.description,
            parameters: this.zodSchemaToJsonSchema(tool.schema)
        }));
    }
    /**
     * Get tools by category
     */
    getToolsByCategory(category) {
        const toolNames = this.toolCategories.get(category) || [];
        return toolNames.map(name => this.tools.get(name)).filter(Boolean);
    }
    /**
     * Get tool by name
     */
    getTool(name) {
        return this.tools.get(name);
    }
    /**
     * Check if tool exists
     */
    hasTool(name) {
        return this.tools.has(name);
    }
    /**
     * Get all registered tools
     */
    getAllTools() {
        return Array.from(this.tools.values());
    }
    /**
     * Get execution history
     */
    getExecutionHistory(limit) {
        if (limit) {
            return this.executionHistory.slice(-limit);
        }
        return [...this.executionHistory];
    }
    /**
     * Clear execution history
     */
    clearExecutionHistory() {
        this.executionHistory = [];
        this.emit('execution-history-cleared');
    }
    /**
     * Get tool usage statistics
     */
    getToolStats() {
        const stats = {};
        for (const tool of this.tools.values()) {
            const toolResults = this.executionHistory.filter(result => result.metadata?.toolName === tool.name);
            stats[tool.name] = {
                totalExecutions: toolResults.length,
                successfulExecutions: toolResults.filter(r => r.success).length,
                failedExecutions: toolResults.filter(r => !r.success).length,
                averageExecutionTime: toolResults.reduce((sum, r) => sum + (r.metadata?.executionTime || 0), 0) / toolResults.length || 0,
                lastExecuted: toolResults.length > 0
                    ? toolResults[toolResults.length - 1].timestamp
                    : null
            };
        }
        return stats;
    }
    /**
     * Validate tool structure
     */
    validateTool(tool) {
        if (!tool.name || typeof tool.name !== 'string') {
            throw new Error('Tool must have a valid name');
        }
        if (!tool.description || typeof tool.description !== 'string') {
            throw new Error('Tool must have a valid description');
        }
        if (!tool.schema || !(tool.schema instanceof zod_1.z.ZodSchema)) {
            throw new Error('Tool must have a valid Zod schema');
        }
        if (!tool.execute || typeof tool.execute !== 'function') {
            throw new Error('Tool must have a valid execute function');
        }
        if (!tool.category) {
            throw new Error('Tool must have a valid category');
        }
        if (this.tools.has(tool.name)) {
            throw new Error(`Tool '${tool.name}' is already registered`);
        }
    }
    /**
     * Convert Zod schema to JSON schema for AI provider
     */
    zodSchemaToJsonSchema(schema) {
        // This is a simplified conversion
        // In a production environment, you'd use a library like zod-to-json-schema
        if (schema instanceof zod_1.z.ZodObject) {
            const shape = schema.shape;
            const properties = {};
            const required = [];
            for (const [key, value] of Object.entries(shape)) {
                properties[key] = this.zodTypeToJsonSchema(value);
                if (!value.isOptional()) {
                    required.push(key);
                }
            }
            return {
                type: 'object',
                properties,
                required: required.length > 0 ? required : undefined
            };
        }
        return this.zodTypeToJsonSchema(schema);
    }
    /**
     * Convert individual Zod types to JSON schema
     */
    zodTypeToJsonSchema(schema) {
        if (schema instanceof zod_1.z.ZodString) {
            return { type: 'string' };
        }
        if (schema instanceof zod_1.z.ZodNumber) {
            return { type: 'number' };
        }
        if (schema instanceof zod_1.z.ZodBoolean) {
            return { type: 'boolean' };
        }
        if (schema instanceof zod_1.z.ZodArray) {
            return {
                type: 'array',
                items: this.zodTypeToJsonSchema(schema.element)
            };
        }
        if (schema instanceof zod_1.z.ZodOptional) {
            return this.zodTypeToJsonSchema(schema.unwrap());
        }
        // Default fallback
        return { type: 'string' };
    }
    /**
     * Unregister a tool
     */
    unregisterTool(toolName) {
        const tool = this.tools.get(toolName);
        if (!tool) {
            return false;
        }
        // Remove from tools map
        this.tools.delete(toolName);
        // Remove from category
        const categoryTools = this.toolCategories.get(tool.category) || [];
        const index = categoryTools.indexOf(toolName);
        if (index > -1) {
            categoryTools.splice(index, 1);
            this.toolCategories.set(tool.category, categoryTools);
        }
        this.emit('tool-unregistered', { name: toolName, category: tool.category });
        return true;
    }
}
exports.ToolRegistry = ToolRegistry;
//# sourceMappingURL=ToolRegistry.js.map