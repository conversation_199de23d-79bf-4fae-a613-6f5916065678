import { EventEmitter } from 'events';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Tool<PERSON><PERSON>ult, ToolCategory, AgentContext } from '../../types';
/**
 * Registry of available tools and their schemas
 * Manages tool registration, validation, and execution
 */
export declare class ToolRegistry extends EventEmitter {
    private tools;
    private toolCategories;
    private executionHistory;
    constructor();
    private initializeCategories;
    /**
     * Register a new tool
     */
    registerTool(toolModule: any): Promise<void>;
    /**
     * Execute a tool with given parameters
     */
    executeTool(toolName: string, parameters: any, context: AgentContext): Promise<ToolResult>;
    /**
     * Execute multiple tools in parallel
     */
    executeToolsParallel(toolCalls: ToolCall[], context: AgentContext): Promise<ToolResult[]>;
    /**
     * Execute multiple tools in sequence
     */
    executeToolsSequence(toolCalls: ToolCall[], context: AgentContext): Promise<ToolResult[]>;
    /**
     * Get tool schemas for AI provider
     */
    getToolSchemas(): any[];
    /**
     * Get tools by category
     */
    getToolsByCategory(category: ToolCategory): Tool[];
    /**
     * Get tool by name
     */
    getTool(name: string): Tool | undefined;
    /**
     * Check if tool exists
     */
    hasTool(name: string): boolean;
    /**
     * Get all registered tools
     */
    getAllTools(): Tool[];
    /**
     * Get execution history
     */
    getExecutionHistory(limit?: number): ToolResult[];
    /**
     * Clear execution history
     */
    clearExecutionHistory(): void;
    /**
     * Get tool usage statistics
     */
    getToolStats(): Record<string, any>;
    /**
     * Validate tool structure
     */
    private validateTool;
    /**
     * Convert Zod schema to JSON schema for AI provider
     */
    private zodSchemaToJsonSchema;
    /**
     * Convert individual Zod types to JSON schema
     */
    private zodTypeToJsonSchema;
    /**
     * Unregister a tool
     */
    unregisterTool(toolName: string): boolean;
}
//# sourceMappingURL=ToolRegistry.d.ts.map