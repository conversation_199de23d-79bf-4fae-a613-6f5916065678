"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigManager = void 0;
const fs_1 = require("fs");
const path_1 = require("path");
const os_1 = require("os");
const types_1 = require("../../types");
class ConfigManager {
    configDir;
    configFile;
    constructor() {
        this.configDir = (0, path_1.join)((0, os_1.homedir)(), '.agent-cli');
        this.configFile = (0, path_1.join)(this.configDir, 'config.json');
    }
    /**
     * Load configuration from file
     */
    async loadConfig() {
        try {
            await this.ensureConfigDir();
            const configData = await fs_1.promises.readFile(this.configFile, 'utf8');
            const config = JSON.parse(configData);
            // Validate configuration
            return types_1.AgentConfigSchema.parse(config);
        }
        catch (error) {
            throw new Error(`Failed to load configuration: ${error.message}`);
        }
    }
    /**
     * Save configuration to file
     */
    async saveConfig(config) {
        try {
            await this.ensureConfigDir();
            // Validate configuration before saving
            const validatedConfig = types_1.AgentConfigSchema.parse(config);
            await fs_1.promises.writeFile(this.configFile, JSON.stringify(validatedConfig, null, 2), 'utf8');
        }
        catch (error) {
            throw new Error(`Failed to save configuration: ${error.message}`);
        }
    }
    /**
     * Check if configuration exists
     */
    async configExists() {
        try {
            await fs_1.promises.access(this.configFile);
            return true;
        }
        catch {
            return false;
        }
    }
    /**
     * Reset configuration (delete config file)
     */
    async resetConfig() {
        try {
            await fs_1.promises.unlink(this.configFile);
        }
        catch (error) {
            // Ignore error if file doesn't exist
            if (error.code !== 'ENOENT') {
                throw new Error(`Failed to reset configuration: ${error.message}`);
            }
        }
    }
    /**
     * Get configuration file path
     */
    getConfigPath() {
        return this.configFile;
    }
    /**
     * Ensure configuration directory exists
     */
    async ensureConfigDir() {
        try {
            await fs_1.promises.mkdir(this.configDir, { recursive: true });
        }
        catch (error) {
            throw new Error(`Failed to create config directory: ${error.message}`);
        }
    }
    /**
     * Update specific configuration values
     */
    async updateConfig(updates) {
        const currentConfig = await this.loadConfig();
        const newConfig = { ...currentConfig, ...updates };
        await this.saveConfig(newConfig);
        return newConfig;
    }
    /**
     * Get default configuration for a provider
     */
    getDefaultConfig(provider) {
        const defaults = {
            openai: {
                provider: 'openai',
                model: 'gpt-4-turbo-preview',
                temperature: 0.7,
                maxTokens: 4000
            },
            deepseek: {
                provider: 'deepseek',
                baseUrl: 'https://api.deepseek.com/v1',
                model: 'deepseek-chat',
                temperature: 0.7,
                maxTokens: 4000
            },
            ollama: {
                provider: 'ollama',
                baseUrl: 'http://localhost:11434/v1',
                model: 'llama2',
                apiKey: 'ollama',
                temperature: 0.7,
                maxTokens: 4000
            }
        };
        return defaults[provider] || defaults.openai;
    }
}
exports.ConfigManager = ConfigManager;
//# sourceMappingURL=ConfigManager.js.map