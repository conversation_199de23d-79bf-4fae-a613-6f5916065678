import { AgentConfig } from '../../types';
export declare class ConfigManager {
    private configDir;
    private configFile;
    constructor();
    /**
     * Load configuration from file
     */
    loadConfig(): Promise<AgentConfig>;
    /**
     * Save configuration to file
     */
    saveConfig(config: AgentConfig): Promise<void>;
    /**
     * Check if configuration exists
     */
    configExists(): Promise<boolean>;
    /**
     * Reset configuration (delete config file)
     */
    resetConfig(): Promise<void>;
    /**
     * Get configuration file path
     */
    getConfigPath(): string;
    /**
     * Ensure configuration directory exists
     */
    private ensureConfigDir;
    /**
     * Update specific configuration values
     */
    updateConfig(updates: Partial<AgentConfig>): Promise<AgentConfig>;
    /**
     * Get default configuration for a provider
     */
    getDefaultConfig(provider: string): Partial<AgentConfig>;
}
//# sourceMappingURL=ConfigManager.d.ts.map