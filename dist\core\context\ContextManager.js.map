{"version": 3, "file": "ContextManager.js", "sourceRoot": "", "sources": ["../../../src/core/context/ContextManager.ts"], "names": [], "mappings": ";;;AAAA,2BAAoC;AACpC,+BAA4B;AAC5B,mCAAgC;AAEhC,kDAA+C;AAE/C,MAAa,cAAc;IACjB,gBAAgB,CAAS;IACzB,WAAW,CAAS;IACpB,UAAU,CAAa;IAE/B,YAAY,gBAAwB;QAClC,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QACzC,IAAI,CAAC,WAAW,GAAG,IAAA,WAAI,EAAC,gBAAgB,EAAE,qBAAqB,CAAC,CAAC;QACjE,IAAI,CAAC,UAAU,GAAG,IAAI,uBAAU,CAAC,gBAAgB,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB;QACrB,MAAM,SAAS,GAAG,IAAA,eAAM,GAAE,CAAC;QAE3B,qCAAqC;QACrC,IAAI,aAAwC,CAAC;QAC7C,IAAI,CAAC;YACH,aAAa,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC;QAC5D,CAAC;QAAC,MAAM,CAAC;YACP,oCAAoC;QACtC,CAAC;QAED,MAAM,OAAO,GAAiB;YAC5B,SAAS;YACT,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,aAAa;YACb,mBAAmB,EAAE,EAAE;YACvB,WAAW,EAAE;gBACX,WAAW;gBACX,YAAY;gBACZ,aAAa;gBACb,gBAAgB;gBAChB,cAAc;gBACd,iBAAiB;gBACjB,qBAAqB;gBACrB,iBAAiB;gBACjB,eAAe;aAChB;YACD,aAAa,EAAE,EAAE;YACjB,aAAa,EAAE,SAAS;SACzB,CAAC;QAEF,+BAA+B;QAC/B,IAAI,CAAC;YACH,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;YACjD,sDAAsD;YACtD,OAAO,CAAC,mBAAmB,GAAG,eAAe,CAAC,mBAAmB,IAAI,EAAE,CAAC;YACxE,OAAO,CAAC,aAAa,GAAG,eAAe,CAAC,aAAa,IAAI,EAAE,CAAC;YAC5D,OAAO,CAAC,aAAa,GAAG,eAAe,CAAC,aAAa,CAAC;QACxD,CAAC;QAAC,MAAM,CAAC;YACP,mCAAmC;QACrC,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW;QACf,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,MAAM,aAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YAChE,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;YAExC,4CAA4C;YAC5C,IAAI,OAAO,CAAC,mBAAmB,EAAE,CAAC;gBAChC,OAAO,CAAC,mBAAmB,GAAG,OAAO,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,GAAQ,EAAE,EAAE,CAAC,CAAC;oBAC3E,GAAG,GAAG;oBACN,SAAS,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC;iBACnC,CAAC,CAAC,CAAC;YACN,CAAC;YAED,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;gBAC1B,OAAO,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,MAAW,EAAE,EAAE,CAAC,CAAC;oBAClE,GAAG,MAAM;oBACT,SAAS,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;iBACtC,CAAC,CAAC,CAAC;YACN,CAAC;YAED,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,2BAA4B,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC;QACzE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,OAAqB;QACrC,IAAI,CAAC;YACH,+CAA+C;YAC/C,MAAM,mBAAmB,GAAG;gBAC1B,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;gBAC1C,aAAa,EAAE,OAAO,CAAC,aAAa;gBACpC,mBAAmB,EAAE,OAAO,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,wBAAwB;gBACrF,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,aAAa,EAAE,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,wBAAwB;gBAC1E,aAAa,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;oBACrC,WAAW,EAAE,OAAO,CAAC,aAAa,CAAC,WAAW;oBAC9C,OAAO,EAAE,OAAO,CAAC,aAAa,CAAC,OAAO;oBACtC,mDAAmD;iBACpD,CAAC,CAAC,CAAC,SAAS;gBACb,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,MAAM,aAAE,CAAC,SAAS,CAChB,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,SAAS,CAAC,mBAAmB,EAAE,IAAI,EAAE,CAAC,CAAC,EAC5C,MAAM,CACP,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,2BAA4B,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC;QACzE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,OAAqB;QACvC,0CAA0C;QAC1C,IAAI,CAAC;YACH,OAAO,CAAC,aAAa,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC;QACpE,CAAC;QAAC,MAAM,CAAC;YACP,yBAAyB;QAC3B,CAAC;QAED,iCAAiC;QACjC,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,sBAAsB,CAAC,OAAqB,EAAE,OAA4B;QACxE,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAE1C,uCAAuC;QACvC,IAAI,OAAO,CAAC,mBAAmB,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;YAC7C,OAAO,CAAC,mBAAmB,GAAG,OAAO,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,OAAqB,EAAE,MAAoB;QACzD,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAEnC,iCAAiC;QACjC,IAAI,OAAO,CAAC,aAAa,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;YACvC,OAAO,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,OAAqB,EAAE,QAAgB,EAAE;QACxD,OAAO,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,sBAAsB,CAAC,OAAqB;QAC1C,MAAM,YAAY,GAAG,OAAO,CAAC,mBAAmB,CAAC,MAAM,CAAC;QACxD,MAAM,YAAY,GAAG,OAAO,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,MAAM,CAAC;QACvF,MAAM,iBAAiB,GAAG,OAAO,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC,MAAM,CAAC;QAEjG,OAAO,GAAG,YAAY,oBAAoB,YAAY,UAAU,iBAAiB,aAAa,CAAC;IACjG,CAAC;IAED;;OAEG;IACH,wBAAwB,CAAC,OAAqB;QAC5C,OAAO,CAAC,mBAAmB,GAAG,EAAE,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,OAAqB;QACtC,OAAO,CAAC,aAAa,GAAG,EAAE,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,OAAqB;QACnC,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC,MAAM,CAChD,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,OAAO,CAAC,YAAY;SAC5E,CAAC;QAEF,OAAO;YACL,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;YAC1C,UAAU,EAAE,CAAC,CAAC,OAAO,CAAC,aAAa;YACnC,oBAAoB,EAAE,OAAO,CAAC,mBAAmB,CAAC,MAAM;YACxD,YAAY,EAAE,OAAO,CAAC,aAAa,CAAC,MAAM;YAC1C,aAAa,EAAE,aAAa,CAAC,MAAM;YACnC,WAAW,EAAE,OAAO,CAAC,WAAW,CAAC,MAAM;YACvC,gBAAgB,EAAE,CAAC,CAAC,OAAO,CAAC,aAAa;SAC1C,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,OAAqB,EAAE,QAAgB;QACzD,IAAI,CAAC;YACH,MAAM,UAAU,GAAG;gBACjB,GAAG,OAAO;gBACV,UAAU,EAAE,IAAI,IAAI,EAAE;gBACtB,OAAO,EAAE,OAAO;aACjB,CAAC;YAEF,MAAM,aAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;QAC5E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,6BAA8B,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,QAAgB;QAClC,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,MAAM,aAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YACxD,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;YAExC,6BAA6B;YAC7B,IAAI,OAAO,CAAC,mBAAmB,EAAE,CAAC;gBAChC,OAAO,CAAC,mBAAmB,GAAG,OAAO,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,GAAQ,EAAE,EAAE,CAAC,CAAC;oBAC3E,GAAG,GAAG;oBACN,SAAS,EAAE,IAAI,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC;iBACnC,CAAC,CAAC,CAAC;YACN,CAAC;YAED,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;gBAC1B,OAAO,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,MAAW,EAAE,EAAE,CAAC,CAAC;oBAClE,GAAG,MAAM;oBACT,SAAS,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;iBACtC,CAAC,CAAC,CAAC;YACN,CAAC;YAED,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,6BAA8B,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa;QACjB,IAAI,CAAC;YACH,MAAM,aAAE,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAClC,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa;QACjB,IAAI,CAAC;YACH,MAAM,aAAE,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QACpC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAK,KAAa,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBACrC,MAAM,IAAI,KAAK,CAAC,6BAA8B,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC;YAC3E,CAAC;QACH,CAAC;IACH,CAAC;CACF;AAzRD,wCAyRC"}