"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentEngine = void 0;
const events_1 = require("events");
const nanoid_1 = require("nanoid");
const types_1 = require("../types");
const AIProvider_1 = require("./ai/AIProvider");
const ToolRegistry_1 = require("./tools/ToolRegistry");
const ContextManager_1 = require("./context/ContextManager");
const DiffTracker_1 = require("./diff/DiffTracker");
const SemanticSearchEngine_1 = require("./semantic/SemanticSearchEngine");
const GitManager_1 = require("./git/GitManager");
/**
 * Central orchestrator that manages the entire agent workflow
 * Coordinates all components and maintains conversation state
 */
class AgentEngine extends events_1.EventEmitter {
    config;
    aiProvider;
    toolRegistry;
    contextManager;
    diffTracker;
    semanticSearch;
    gitManager;
    context;
    isProcessing = false;
    constructor(config, workingDirectory) {
        super();
        this.config = config;
        this.initializeComponents(workingDirectory).catch(error => {
            this.emit('error', new types_1.AgentError(`Failed to initialize agent: ${error.message}`, 'initialization-failed', 'system', false, { originalError: error }));
        });
    }
    async initializeComponents(workingDirectory) {
        // Initialize core components
        this.aiProvider = new AIProvider_1.AIProvider(this.config);
        this.toolRegistry = new ToolRegistry_1.ToolRegistry();
        this.contextManager = new ContextManager_1.ContextManager(workingDirectory);
        this.diffTracker = new DiffTracker_1.DiffTracker();
        this.semanticSearch = new SemanticSearchEngine_1.SemanticSearchEngine();
        this.gitManager = new GitManager_1.GitManager(workingDirectory);
        // Initialize context
        this.context = await this.contextManager.initializeContext();
        // Register event handlers
        this.setupEventHandlers();
        // Register core tools
        await this.registerCoreTools();
        this.emit('initialized', { context: this.context });
    }
    setupEventHandlers() {
        // Tool execution events
        this.toolRegistry.on('tool-executed', (result) => {
            this.handleToolResult(result);
        });
        // Diff tracking events
        this.diffTracker.on('change-detected', (change) => {
            this.context.changeHistory.push(change);
            this.emit('change-tracked', change);
        });
        // Git events
        this.gitManager.on('git-operation', (operation) => {
            this.emit('git-operation', operation);
        });
        // Error handling
        this.on('error', (error) => {
            this.handleError(error);
        });
    }
    async registerCoreTools() {
        // Import and register file operations tools
        const fileOpsModule = await Promise.resolve().then(() => __importStar(require('../tools/FileOperations')));
        const fileOpsTools = fileOpsModule.default;
        for (const tool of fileOpsTools) {
            await this.toolRegistry.registerTool(tool);
        }
        // Import and register shell command tools
        const shellModule = await Promise.resolve().then(() => __importStar(require('../tools/ShellCommands')));
        const shellTools = shellModule.default;
        for (const tool of shellTools) {
            await this.toolRegistry.registerTool(tool);
        }
        // Import and register git operation tools
        const gitModule = await Promise.resolve().then(() => __importStar(require('../tools/GitOperations')));
        const gitTools = gitModule.default;
        for (const tool of gitTools) {
            await this.toolRegistry.registerTool(tool);
        }
        // Import and register semantic search tools
        const semanticModule = await Promise.resolve().then(() => __importStar(require('../tools/SemanticSearch')));
        const semanticTools = semanticModule.default;
        for (const tool of semanticTools) {
            await this.toolRegistry.registerTool(tool);
        }
        // Import and register context management tools
        const contextModule = await Promise.resolve().then(() => __importStar(require('../tools/ContextManagement')));
        const contextTools = contextModule.default;
        for (const tool of contextTools) {
            await this.toolRegistry.registerTool(tool);
        }
    }
    /**
     * Process user input and execute autonomous workflow
     */
    async processInput(input) {
        if (this.isProcessing) {
            throw new types_1.AgentError('Agent is already processing a request', 'agent-busy', 'user-input', false);
        }
        this.isProcessing = true;
        try {
            // Create user message
            const userMessage = {
                id: (0, nanoid_1.nanoid)(),
                role: 'user',
                content: input,
                timestamp: new Date()
            };
            // Add to conversation history
            this.context.conversationHistory.push(userMessage);
            // Emit processing start event
            this.emit('processing-started', { input, context: this.context });
            // Start autonomous execution loop
            await this.autonomousExecutionLoop();
        }
        catch (error) {
            this.handleError(error);
        }
        finally {
            this.isProcessing = false;
            this.emit('processing-completed');
        }
    }
    /**
     * Autonomous execution loop - continues until task completion
     */
    async autonomousExecutionLoop() {
        let maxIterations = 50; // Prevent infinite loops
        let iteration = 0;
        while (iteration < maxIterations) {
            iteration++;
            try {
                // Get AI response with tool calls
                const response = await this.aiProvider.getResponse(this.context.conversationHistory, this.toolRegistry.getToolSchemas(), this.context);
                // Add AI response to conversation
                const assistantMessage = {
                    id: (0, nanoid_1.nanoid)(),
                    role: 'assistant',
                    content: response.content,
                    timestamp: new Date(),
                    toolCalls: response.toolCalls
                };
                this.context.conversationHistory.push(assistantMessage);
                // Execute tool calls if present
                if (response.toolCalls && response.toolCalls.length > 0) {
                    const toolResults = await this.executeToolCalls(response.toolCalls);
                    assistantMessage.toolResults = toolResults;
                    // Check if more iterations are needed
                    const needsContinuation = this.shouldContinueExecution(toolResults);
                    if (!needsContinuation) {
                        break;
                    }
                }
                else {
                    // No tool calls, task is complete
                    break;
                }
                // Update context after each iteration
                await this.contextManager.updateContext(this.context);
            }
            catch (error) {
                this.handleError(error);
                break;
            }
        }
        if (iteration >= maxIterations) {
            this.emit('max-iterations-reached', { iterations: maxIterations });
        }
    }
    /**
     * Execute multiple tool calls in sequence or parallel
     */
    async executeToolCalls(toolCalls) {
        const results = [];
        for (const toolCall of toolCalls) {
            try {
                this.emit('tool-execution-started', toolCall);
                const result = await this.toolRegistry.executeTool(toolCall.name, toolCall.parameters, this.context);
                results.push(result);
                this.emit('tool-execution-completed', result);
            }
            catch (error) {
                const errorResult = {
                    id: (0, nanoid_1.nanoid)(),
                    toolCallId: toolCall.id,
                    success: false,
                    output: null,
                    error: error.message,
                    timestamp: new Date()
                };
                results.push(errorResult);
                this.emit('tool-execution-failed', errorResult);
            }
        }
        return results;
    }
    /**
     * Determine if execution should continue based on tool results
     */
    shouldContinueExecution(toolResults) {
        // Continue if any tool indicates more work is needed
        return toolResults.some(result => result.metadata?.continueExecution === true ||
            result.metadata?.requiresFollowup === true);
    }
    /**
     * Handle tool execution results
     */
    handleToolResult(result) {
        // Update context based on tool result
        if (result.success && result.metadata?.contextUpdate) {
            Object.assign(this.context, result.metadata.contextUpdate);
        }
        // Track changes if file operations occurred
        if (result.metadata?.fileChanges) {
            this.diffTracker.trackChanges(result.metadata.fileChanges);
        }
        this.emit('tool-result-processed', result);
    }
    /**
     * Handle errors with recovery strategies
     */
    handleError(error) {
        console.error('Agent Error:', error);
        // Implement recovery strategies based on error type
        if (error.recoverable) {
            this.emit('error-recovery-attempted', error);
            // Implement specific recovery logic
        }
        else {
            this.emit('fatal-error', error);
        }
    }
    /**
     * Get current agent context
     */
    getContext() {
        return { ...this.context };
    }
    /**
     * Update agent configuration
     */
    async updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        this.aiProvider.updateConfig(this.config);
        this.emit('config-updated', this.config);
    }
    /**
     * Reset agent state
     */
    async reset() {
        this.context = await this.contextManager.initializeContext();
        this.isProcessing = false;
        this.emit('reset-completed');
    }
    /**
     * Graceful shutdown
     */
    async shutdown() {
        this.isProcessing = false;
        await this.contextManager.saveContext(this.context);
        this.emit('shutdown-completed');
    }
}
exports.AgentEngine = AgentEngine;
//# sourceMappingURL=AgentEngine.js.map