import { EventEmitter } from 'events';
import { SemanticSearchResult } from '../../types';
/**
 * Semantic Search Engine for code understanding
 * This is a placeholder implementation for Phase 1
 * Full implementation will be added in Phase 2
 */
export declare class SemanticSearchEngine extends EventEmitter {
    private index;
    constructor();
    /**
     * Initialize semantic index
     */
    initializeIndex(): Promise<void>;
    /**
     * Index a file for semantic search
     */
    indexFile(filePath: string, content: string): Promise<void>;
    /**
     * Search for code using natural language query
     */
    search(query: string, limit?: number): Promise<SemanticSearchResult[]>;
    /**
     * Update index for a file
     */
    updateFile(filePath: string, content: string): Promise<void>;
    /**
     * Remove file from index
     */
    removeFile(filePath: string): Promise<void>;
    /**
     * Get index statistics
     */
    getIndexStats(): Record<string, any>;
    /**
     * Detect programming language from file path
     */
    private detectLanguage;
    /**
     * Extract symbols from code (placeholder)
     */
    private extractSymbols;
    /**
     * Extract imports from code (placeholder)
     */
    private extractImports;
    /**
     * Extract exports from code (placeholder)
     */
    private extractExports;
    /**
     * Get context around a line
     */
    private getContext;
    /**
     * Clear the entire index
     */
    clearIndex(): Promise<void>;
    /**
     * Check if index exists
     */
    hasIndex(): boolean;
}
//# sourceMappingURL=SemanticSearchEngine.d.ts.map