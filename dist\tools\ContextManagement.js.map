{"version": 3, "file": "ContextManagement.js", "sourceRoot": "", "sources": ["../../src/tools/ContextManagement.ts"], "names": [], "mappings": ";;;AAAA,6BAAwB;AACxB,mCAAgC;AAGhC,mBAAmB;AACnB,MAAM,gBAAgB,GAAG,OAAC,CAAC,MAAM,CAAC;IAChC,cAAc,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,8BAA8B,CAAC;IAC7F,cAAc,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,wBAAwB,CAAC;IACvF,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,yBAAyB,CAAC;CAC7E,CAAC,CAAC;AAEU,QAAA,cAAc,GAAS;IAClC,IAAI,EAAE,aAAa;IACnB,WAAW,EAAE,mDAAmD;IAChE,MAAM,EAAE,gBAAgB;IACxB,QAAQ,EAAE,oBAAoB;IAC9B,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,OAAqB;QACzC,IAAI,CAAC;YACH,MAAM,WAAW,GAAQ;gBACvB,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;gBAC1C,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,aAAa,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;oBACrC,aAAa,EAAE,OAAO,CAAC,aAAa,CAAC,aAAa;oBAClD,UAAU,EAAE,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC;oBACpD,UAAU,EAAE,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC;wBAChD,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC;wBAC7C,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC;iBAC5D,CAAC,CAAC,CAAC,IAAI;aACT,CAAC;YAEF,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;gBAC1B,WAAW,CAAC,mBAAmB,GAAG,OAAO,CAAC,mBAAmB;qBAC1D,KAAK,CAAC,CAAC,MAAM,CAAC,KAAM,CAAC;qBACrB,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;oBACX,EAAE,EAAE,GAAG,CAAC,EAAE;oBACV,IAAI,EAAE,GAAG,CAAC,IAAI;oBACd,OAAO,EAAE,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;oBAChF,SAAS,EAAE,GAAG,CAAC,SAAS;oBACxB,YAAY,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,IAAI,GAAG,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;iBAC5D,CAAC,CAAC,CAAC;YACR,CAAC;YAED,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;gBAC1B,WAAW,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa;qBAC9C,KAAK,CAAC,CAAC,MAAM,CAAC,KAAM,CAAC;qBACrB,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;oBACd,EAAE,EAAE,MAAM,CAAC,EAAE;oBACb,IAAI,EAAE,MAAM,CAAC,IAAI;oBACjB,IAAI,EAAE,MAAM,CAAC,IAAI;oBACjB,SAAS,EAAE,MAAM,CAAC,SAAS;oBAC3B,OAAO,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI;iBACvB,CAAC,CAAC,CAAC;YACR,CAAC;YAED,OAAO;gBACL,EAAE,EAAE,IAAA,eAAM,GAAE;gBACZ,UAAU,EAAE,IAAA,eAAM,GAAE;gBACpB,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,WAAW;gBACnB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,QAAQ,EAAE;oBACR,SAAS,EAAE,aAAa;oBACxB,cAAc,EAAE,MAAM,CAAC,cAAc;oBACrC,cAAc,EAAE,MAAM,CAAC,cAAc;iBACtC;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,EAAE,EAAE,IAAA,eAAM,GAAE;gBACZ,UAAU,EAAE,IAAA,eAAM,GAAE;gBACpB,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,IAAI;gBACZ,KAAK,EAAE,0BAA2B,KAAe,CAAC,OAAO,EAAE;gBAC3D,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAC;AAEF,qBAAqB;AACrB,MAAM,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;IAClC,IAAI,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,cAAc,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,0BAA0B,CAAC;IACrF,OAAO,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,kDAAkD,CAAC;CACjG,CAAC,CAAC;AAEU,QAAA,gBAAgB,GAAS;IACpC,IAAI,EAAE,eAAe;IACrB,WAAW,EAAE,sCAAsC;IACnD,MAAM,EAAE,kBAAkB;IAC1B,QAAQ,EAAE,oBAAoB;IAC9B,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,OAAqB;QACzC,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBACpB,OAAO;oBACL,EAAE,EAAE,IAAA,eAAM,GAAE;oBACZ,UAAU,EAAE,IAAA,eAAM,GAAE;oBACpB,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,IAAI;oBACZ,KAAK,EAAE,2DAA2D;oBAClE,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC;YACJ,CAAC;YAED,IAAI,OAAO,GAAa,EAAE,CAAC;YAE3B,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;gBACpB,KAAK,cAAc;oBACjB,OAAO,CAAC,mBAAmB,GAAG,EAAE,CAAC;oBACjC,OAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;oBACrC,MAAM;gBAER,KAAK,SAAS;oBACZ,OAAO,CAAC,aAAa,GAAG,EAAE,CAAC;oBAC3B,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;oBAC/B,MAAM;gBAER,KAAK,KAAK;oBACR,OAAO,CAAC,mBAAmB,GAAG,EAAE,CAAC;oBACjC,OAAO,CAAC,aAAa,GAAG,EAAE,CAAC;oBAC3B,OAAO,CAAC,IAAI,CAAC,sBAAsB,EAAE,gBAAgB,CAAC,CAAC;oBACvD,MAAM;YACV,CAAC;YAED,OAAO;gBACL,EAAE,EAAE,IAAA,eAAM,GAAE;gBACZ,UAAU,EAAE,IAAA,eAAM,GAAE;gBACpB,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE;oBACN,OAAO;oBACP,IAAI,EAAE,MAAM,CAAC,IAAI;iBAClB;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,QAAQ,EAAE;oBACR,SAAS,EAAE,eAAe;oBAC1B,IAAI,EAAE,MAAM,CAAC,IAAI;iBAClB;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,EAAE,EAAE,IAAA,eAAM,GAAE;gBACZ,UAAU,EAAE,IAAA,eAAM,GAAE;gBACpB,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,IAAI;gBACZ,KAAK,EAAE,4BAA6B,KAAe,CAAC,OAAO,EAAE;gBAC7D,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAC;AAEF,yBAAyB;AACzB,MAAM,qBAAqB,GAAG,OAAC,CAAC,MAAM,CAAC;IACrC,QAAQ,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,6BAA6B,CAAC;CACxF,CAAC,CAAC;AAEU,QAAA,mBAAmB,GAAS;IACvC,IAAI,EAAE,mBAAmB;IACzB,WAAW,EAAE,0CAA0C;IACvD,MAAM,EAAE,qBAAqB;IAC7B,QAAQ,EAAE,oBAAoB;IAC9B,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,OAAqB;QACzC,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC,MAAM,CAChD,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,OAAO,CAAC,YAAY;aAC5E,CAAC;YAEF,MAAM,YAAY,GAAG,OAAO,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC;YAChF,MAAM,iBAAiB,GAAG,OAAO,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC;YAC1F,MAAM,SAAS,GAAG,OAAO,CAAC,mBAAmB,CAAC,MAAM,CAClD,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,IAAI,CAAC,CAAC,EAAE,CAAC,CACxD,CAAC;YAEF,MAAM,KAAK,GAAG;gBACZ,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;gBAC1C,YAAY,EAAE;oBACZ,aAAa,EAAE,OAAO,CAAC,mBAAmB,CAAC,MAAM;oBACjD,YAAY,EAAE,YAAY,CAAC,MAAM;oBACjC,iBAAiB,EAAE,iBAAiB,CAAC,MAAM;oBAC3C,SAAS;iBACV;gBACD,OAAO,EAAE;oBACP,YAAY,EAAE,OAAO,CAAC,aAAa,CAAC,MAAM;oBAC1C,aAAa,EAAE,aAAa,CAAC,MAAM;oBACnC,cAAc,EAAE,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAC/C,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM;oBACpC,aAAa,EAAE,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAC9C,CAAC,CAAC,IAAI,KAAK,eAAe,CAAC,CAAC,MAAM;iBACrC;gBACD,KAAK,EAAE;oBACL,WAAW,EAAE,OAAO,CAAC,WAAW,CAAC,MAAM;oBACvC,mBAAmB,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;4BAC9D,sCAAsC;4BACtC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;gCAAE,OAAO,iBAAiB,CAAC;4BACpD,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;gCAAE,OAAO,gBAAgB,CAAC;4BAClD,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;gCAAE,OAAO,gBAAgB,CAAC;4BAClF,OAAO,OAAO,CAAC;wBACjB,CAAC,CAAC,CAAC,CAAC;iBACL;aACF,CAAC;YAEF,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;gBACpB,KAAK,CAAC,UAAU,CAAC,GAAG;oBAClB,aAAa,EAAE,OAAO,CAAC,aAAa;oBACpC,aAAa,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;wBACrC,QAAQ,EAAE,IAAI;wBACd,WAAW,EAAE,OAAO,CAAC,aAAa,CAAC,WAAW;wBAC9C,OAAO,EAAE,OAAO,CAAC,aAAa,CAAC,OAAO;qBACvC,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE;oBACvB,WAAW,EAAE,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;wBACxD,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;wBAC/C,OAAO,GAAG,CAAC;oBACb,CAAC,EAAE,EAA4B,CAAC;iBACjC,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,EAAE,EAAE,IAAA,eAAM,GAAE;gBACZ,UAAU,EAAE,IAAA,eAAM,GAAE;gBACpB,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE,KAAK;gBACb,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,QAAQ,EAAE;oBACR,SAAS,EAAE,eAAe;oBAC1B,QAAQ,EAAE,MAAM,CAAC,QAAQ;iBAC1B;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,EAAE,EAAE,IAAA,eAAM,GAAE;gBACZ,UAAU,EAAE,IAAA,eAAM,GAAE;gBACpB,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,IAAI;gBACZ,KAAK,EAAE,gCAAiC,KAAe,CAAC,OAAO,EAAE;gBACjE,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAC;AAEF,sBAAsB;AACtB,MAAM,mBAAmB,GAAG,OAAC,CAAC,MAAM,CAAC;IACnC,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,kCAAkC,CAAC;IACjE,cAAc,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,8BAA8B,CAAC;IAC7F,cAAc,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,wBAAwB,CAAC;CACxF,CAAC,CAAC;AAEU,QAAA,iBAAiB,GAAS;IACrC,IAAI,EAAE,gBAAgB;IACtB,WAAW,EAAE,yDAAyD;IACtE,MAAM,EAAE,mBAAmB;IAC3B,QAAQ,EAAE,oBAAoB;IAC9B,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,OAAqB;QACzC,IAAI,CAAC;YACH,MAAM,UAAU,GAAQ;gBACtB,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;gBAC1C,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,aAAa,EAAE,OAAO,CAAC,aAAa;gBACpC,UAAU,EAAE,IAAI,IAAI,EAAE;gBACtB,OAAO,EAAE,OAAO;aACjB,CAAC;YAEF,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;gBAC1B,UAAU,CAAC,mBAAmB,GAAG,OAAO,CAAC,mBAAmB,CAAC;YAC/D,CAAC;YAED,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;gBAC1B,UAAU,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;YACnD,CAAC;YAED,gEAAgE;YAChE,gDAAgD;YAEhD,OAAO;gBACL,EAAE,EAAE,IAAA,eAAM,GAAE;gBACZ,UAAU,EAAE,IAAA,eAAM,GAAE;gBACpB,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE;oBACN,QAAQ,EAAE,MAAM,CAAC,QAAQ;oBACzB,QAAQ,EAAE,IAAI;oBACd,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,MAAM;oBAC3C,cAAc,EAAE,MAAM,CAAC,cAAc;oBACrC,cAAc,EAAE,MAAM,CAAC,cAAc;iBACtC;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,QAAQ,EAAE;oBACR,SAAS,EAAE,gBAAgB;oBAC3B,QAAQ,EAAE,MAAM,CAAC,QAAQ;oBACzB,gBAAgB,EAAE,IAAI,CAAC,kCAAkC;iBAC1D;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,EAAE,EAAE,IAAA,eAAM,GAAE;gBACZ,UAAU,EAAE,IAAA,eAAM,GAAE;gBACpB,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,IAAI;gBACZ,KAAK,EAAE,6BAA8B,KAAe,CAAC,OAAO,EAAE;gBAC9D,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAC;AAUF,kBAAe;IACb,sBAAc;IACd,wBAAgB;IAChB,2BAAmB;IACnB,yBAAiB;CAClB,CAAC"}