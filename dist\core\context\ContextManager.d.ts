import { AgentContext, ConversationMessage, ChangeRecord } from '../../types';
export declare class ContextManager {
    private workingDirectory;
    private contextFile;
    private gitManager;
    constructor(workingDirectory: string);
    /**
     * Initialize a new agent context
     */
    initializeContext(): Promise<AgentContext>;
    /**
     * Load context from file
     */
    loadContext(): Promise<Partial<AgentContext>>;
    /**
     * Save context to file
     */
    saveContext(context: AgentContext): Promise<void>;
    /**
     * Update context with new information
     */
    updateContext(context: AgentContext): Promise<void>;
    /**
     * Add conversation message to context
     */
    addConversationMessage(context: AgentContext, message: ConversationMessage): void;
    /**
     * Add change record to context
     */
    addChangeRecord(context: AgentContext, change: ChangeRecord): void;
    /**
     * Get recent changes
     */
    getRecentChanges(context: AgentContext, limit?: number): ChangeRecord[];
    /**
     * Get conversation summary
     */
    getConversationSummary(context: AgentContext): string;
    /**
     * Clear conversation history
     */
    clearConversationHistory(context: AgentContext): void;
    /**
     * Clear change history
     */
    clearChangeHistory(context: AgentContext): void;
    /**
     * Get context statistics
     */
    getContextStats(context: AgentContext): Record<string, any>;
    /**
     * Export context for backup
     */
    exportContext(context: AgentContext, filePath: string): Promise<void>;
    /**
     * Import context from backup
     */
    importContext(filePath: string): Promise<Partial<AgentContext>>;
    /**
     * Check if context file exists
     */
    contextExists(): Promise<boolean>;
    /**
     * Delete context file
     */
    deleteContext(): Promise<void>;
}
//# sourceMappingURL=ContextManager.d.ts.map