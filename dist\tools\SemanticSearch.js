"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getIndexStatsTool = exports.indexFileTool = exports.semanticSearchTool = void 0;
const zod_1 = require("zod");
const nanoid_1 = require("nanoid");
const SemanticSearchEngine_1 = require("../core/semantic/SemanticSearchEngine");
// Semantic Search Tool
const semanticSearchSchema = zod_1.z.object({
    query: zod_1.z.string().describe('Natural language query to search for in code'),
    limit: zod_1.z.number().default(10).optional().describe('Maximum number of results to return'),
    fileTypes: zod_1.z.array(zod_1.z.string()).optional().describe('File extensions to search in (e.g., ["ts", "js"])')
});
exports.semanticSearchTool = {
    name: 'semantic_search',
    description: 'Search for code using natural language queries with semantic understanding',
    schema: semanticSearchSchema,
    category: 'semantic-search',
    async execute(params, context) {
        try {
            const searchEngine = new SemanticSearchEngine_1.SemanticSearchEngine();
            // Initialize index if not already done
            if (!searchEngine.hasIndex()) {
                await searchEngine.initializeIndex();
            }
            const results = await searchEngine.search(params.query, params.limit);
            // Filter by file types if specified
            const filteredResults = params.fileTypes
                ? results.filter(result => {
                    const ext = result.filePath.split('.').pop()?.toLowerCase();
                    return params.fileTypes.includes(ext || '');
                })
                : results;
            return {
                id: (0, nanoid_1.nanoid)(),
                toolCallId: (0, nanoid_1.nanoid)(),
                success: true,
                output: {
                    query: params.query,
                    results: filteredResults,
                    totalResults: filteredResults.length,
                    searchStats: searchEngine.getIndexStats()
                },
                timestamp: new Date(),
                metadata: {
                    operation: 'semantic-search',
                    queryType: 'natural-language',
                    resultCount: filteredResults.length
                }
            };
        }
        catch (error) {
            return {
                id: (0, nanoid_1.nanoid)(),
                toolCallId: (0, nanoid_1.nanoid)(),
                success: false,
                output: null,
                error: `Semantic search failed: ${error.message}`,
                timestamp: new Date()
            };
        }
    }
};
// Index File Tool
const indexFileSchema = zod_1.z.object({
    filePath: zod_1.z.string().describe('Path to the file to index'),
    content: zod_1.z.string().optional().describe('File content (if not provided, will read from file)')
});
exports.indexFileTool = {
    name: 'index_file',
    description: 'Index a file for semantic search',
    schema: indexFileSchema,
    category: 'semantic-search',
    async execute(params, context) {
        try {
            const searchEngine = new SemanticSearchEngine_1.SemanticSearchEngine();
            let content = params.content;
            if (!content) {
                // Would read file content here
                // For now, return error asking for content
                throw new Error('File content must be provided for indexing');
            }
            await searchEngine.indexFile(params.filePath, content);
            return {
                id: (0, nanoid_1.nanoid)(),
                toolCallId: (0, nanoid_1.nanoid)(),
                success: true,
                output: {
                    filePath: params.filePath,
                    indexed: true,
                    contentLength: content.length
                },
                timestamp: new Date(),
                metadata: {
                    operation: 'index-file',
                    filePath: params.filePath
                }
            };
        }
        catch (error) {
            return {
                id: (0, nanoid_1.nanoid)(),
                toolCallId: (0, nanoid_1.nanoid)(),
                success: false,
                output: null,
                error: `File indexing failed: ${error.message}`,
                timestamp: new Date()
            };
        }
    }
};
// Get Index Stats Tool
const getIndexStatsSchema = zod_1.z.object({
    detailed: zod_1.z.boolean().default(false).optional().describe('Include detailed statistics')
});
exports.getIndexStatsTool = {
    name: 'get_index_stats',
    description: 'Get statistics about the semantic search index',
    schema: getIndexStatsSchema,
    category: 'semantic-search',
    async execute(params, context) {
        try {
            const searchEngine = new SemanticSearchEngine_1.SemanticSearchEngine();
            const stats = searchEngine.getIndexStats();
            return {
                id: (0, nanoid_1.nanoid)(),
                toolCallId: (0, nanoid_1.nanoid)(),
                success: true,
                output: {
                    stats,
                    detailed: params.detailed
                },
                timestamp: new Date(),
                metadata: {
                    operation: 'index-stats'
                }
            };
        }
        catch (error) {
            return {
                id: (0, nanoid_1.nanoid)(),
                toolCallId: (0, nanoid_1.nanoid)(),
                success: false,
                output: null,
                error: `Failed to get index stats: ${error.message}`,
                timestamp: new Date()
            };
        }
    }
};
exports.default = [
    exports.semanticSearchTool,
    exports.indexFileTool,
    exports.getIndexStatsTool
];
//# sourceMappingURL=SemanticSearch.js.map