"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolCallSchema = exports.AgentConfigSchema = exports.AgentError = void 0;
const zod_1 = require("zod");
// Error and Event Types
class AgentError extends Error {
    code;
    category;
    recoverable;
    context;
    constructor(message, code, category, recoverable, context) {
        super(message);
        this.name = 'AgentError';
        this.code = code;
        this.category = category;
        this.recoverable = recoverable;
        this.context = context;
    }
}
exports.AgentError = AgentError;
// Configuration Schemas
exports.AgentConfigSchema = zod_1.z.object({
    provider: zod_1.z.enum(['openai', 'deepseek', 'ollama']),
    apiKey: zod_1.z.string().min(1),
    baseUrl: zod_1.z.string().url().optional(),
    model: zod_1.z.string().min(1),
    temperature: zod_1.z.number().min(0).max(2).optional(),
    maxTokens: zod_1.z.number().positive().optional(),
});
exports.ToolCallSchema = zod_1.z.object({
    id: zod_1.z.string(),
    name: zod_1.z.string(),
    parameters: zod_1.z.record(zod_1.z.any()),
});
//# sourceMappingURL=index.js.map