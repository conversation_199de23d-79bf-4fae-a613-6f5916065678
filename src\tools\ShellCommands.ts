import { spawn, exec } from 'child_process';
import { promisify } from 'util';
import { z } from 'zod';
import { nanoid } from 'nanoid';
import { Tool, ToolResult, AgentContext } from '../types';

const execAsync = promisify(exec);

// Execute Shell Command Tool
const executeCommandSchema = z.object({
  command: z.string().describe('Shell command to execute'),
  args: z.array(z.string()).optional().describe('Command arguments'),
  cwd: z.string().optional().describe('Working directory for command execution'),
  timeout: z.number().default(30000).optional().describe('Timeout in milliseconds'),
  env: z.record(z.string()).optional().describe('Environment variables'),
  shell: z.boolean().default(true).optional().describe('Execute in shell')
});

const executeCommandTool: Tool = {
  name: 'execute_command',
  description: 'Execute a shell command and return the output',
  schema: executeCommandSchema,
  category: 'shell-commands',
  async execute(params, context: AgentContext): Promise<ToolResult> {
    try {
      const workingDir = params.cwd 
        ? (params.cwd.startsWith('/') ? params.cwd : `${context.workingDirectory}/${params.cwd}`)
        : context.workingDirectory;

      const fullCommand = params.args 
        ? `${params.command} ${params.args.join(' ')}`
        : params.command;

      const options = {
        cwd: workingDir,
        timeout: params.timeout || 30000,
        env: { ...process.env, ...params.env },
        shell: params.shell !== false ? true : '/bin/bash'
      };

      const { stdout, stderr } = await execAsync(fullCommand, options);

      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: true,
        output: {
          command: fullCommand,
          stdout: stdout.trim(),
          stderr: stderr.trim(),
          exitCode: 0,
          workingDirectory: workingDir
        },
        timestamp: new Date(),
        metadata: {
          operation: 'execute',
          command: fullCommand,
          executionTime: Date.now()
        }
      };
    } catch (error: any) {
      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: false,
        output: {
          command: params.command,
          stdout: error.stdout || '',
          stderr: error.stderr || error.message,
          exitCode: error.code || 1,
          workingDirectory: context.workingDirectory
        },
        error: `Command failed: ${error.message}`,
        timestamp: new Date()
      };
    }
  }
};

// Interactive Shell Command Tool
const interactiveCommandSchema = z.object({
  command: z.string().describe('Command to start interactive session'),
  inputs: z.array(z.string()).optional().describe('Inputs to send to the interactive session'),
  timeout: z.number().default(60000).optional().describe('Timeout in milliseconds'),
  cwd: z.string().optional().describe('Working directory')
});

const interactiveCommandTool: Tool = {
  name: 'interactive_command',
  description: 'Execute an interactive command with input/output handling',
  schema: interactiveCommandSchema,
  category: 'shell-commands',
  async execute(params, context: AgentContext): Promise<ToolResult> {
    return new Promise((resolve) => {
      try {
        const workingDir = params.cwd 
          ? `${context.workingDirectory}/${params.cwd}`
          : context.workingDirectory;

        const child = spawn(params.command, [], {
          cwd: workingDir,
          stdio: ['pipe', 'pipe', 'pipe'],
          shell: true
        });

        let stdout = '';
        let stderr = '';
        let isResolved = false;

        const timeout = setTimeout(() => {
          if (!isResolved) {
            child.kill();
            resolve({
              id: nanoid(),
              toolCallId: nanoid(),
              success: false,
              output: { stdout, stderr, exitCode: -1 },
              error: 'Command timed out',
              timestamp: new Date()
            });
            isResolved = true;
          }
        }, params.timeout || 60000);

        child.stdout?.on('data', (data) => {
          stdout += data.toString();
        });

        child.stderr?.on('data', (data) => {
          stderr += data.toString();
        });

        child.on('close', (code) => {
          if (!isResolved) {
            clearTimeout(timeout);
            resolve({
              id: nanoid(),
              toolCallId: nanoid(),
              success: code === 0,
              output: {
                command: params.command,
                stdout: stdout.trim(),
                stderr: stderr.trim(),
                exitCode: code || 0,
                workingDirectory: workingDir
              },
              timestamp: new Date(),
              metadata: {
                operation: 'interactive',
                command: params.command
              }
            });
            isResolved = true;
          }
        });

        child.on('error', (error) => {
          if (!isResolved) {
            clearTimeout(timeout);
            resolve({
              id: nanoid(),
              toolCallId: nanoid(),
              success: false,
              output: { stdout, stderr, exitCode: -1 },
              error: `Process error: ${error.message}`,
              timestamp: new Date()
            });
            isResolved = true;
          }
        });

        // Send inputs if provided
        if (params.inputs && params.inputs.length > 0) {
          params.inputs.forEach((input, index) => {
            setTimeout(() => {
              child.stdin?.write(input + '\n');
              if (index === params.inputs!.length - 1) {
                child.stdin?.end();
              }
            }, index * 1000);
          });
        } else {
          child.stdin?.end();
        }

      } catch (error) {
        resolve({
          id: nanoid(),
          toolCallId: nanoid(),
          success: false,
          output: null,
          error: `Failed to start interactive command: ${(error as Error).message}`,
          timestamp: new Date()
        });
      }
    });
  }
};

// Get System Information Tool
const getSystemInfoSchema = z.object({
  detailed: z.boolean().default(false).optional().describe('Include detailed system information')
});

const getSystemInfoTool: Tool = {
  name: 'get_system_info',
  description: 'Get system information including OS, architecture, and environment',
  schema: getSystemInfoSchema,
  category: 'shell-commands',
  async execute(params, context: AgentContext): Promise<ToolResult> {
    try {
      const basicInfo = {
        platform: process.platform,
        architecture: process.arch,
        nodeVersion: process.version,
        workingDirectory: context.workingDirectory,
        environment: process.env.NODE_ENV || 'development'
      };

      let detailedInfo: Record<string, string> = {};

      if (params.detailed) {
        try {
          // Get additional system information
          const commands = {
            os: process.platform === 'win32' ? 'ver' : 'uname -a',
            memory: process.platform === 'win32' ? 'wmic computersystem get TotalPhysicalMemory' : 'free -h',
            disk: process.platform === 'win32' ? 'wmic logicaldisk get size,freespace,caption' : 'df -h',
            cpu: process.platform === 'win32' ? 'wmic cpu get name' : 'lscpu'
          };

          for (const [key, command] of Object.entries(commands)) {
            try {
              const { stdout } = await execAsync(command, { timeout: 5000 });
              detailedInfo[key] = stdout.trim();
            } catch {
              detailedInfo[key] = 'Not available';
            }
          }
        } catch {
          // Ignore errors in detailed info gathering
        }
      }

      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: true,
        output: {
          ...basicInfo,
          ...(params.detailed && { detailed: detailedInfo })
        },
        timestamp: new Date(),
        metadata: {
          operation: 'system-info',
          detailed: params.detailed
        }
      };
    } catch (error) {
      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: false,
        output: null,
        error: `Failed to get system info: ${(error as Error).message}`,
        timestamp: new Date()
      };
    }
  }
};

// Check Process Tool
const checkProcessSchema = z.object({
  processName: z.string().optional().describe('Process name to check'),
  pid: z.number().optional().describe('Process ID to check'),
  port: z.number().optional().describe('Port to check if in use')
});

const checkProcessTool: Tool = {
  name: 'check_process',
  description: 'Check if a process is running or if a port is in use',
  schema: checkProcessSchema,
  category: 'shell-commands',
  async execute(params, context: AgentContext): Promise<ToolResult> {
    try {
      let command = '';
      let searchTarget = '';

      if (params.pid) {
        command = process.platform === 'win32' 
          ? `tasklist /FI "PID eq ${params.pid}"` 
          : `ps -p ${params.pid}`;
        searchTarget = `PID ${params.pid}`;
      } else if (params.processName) {
        command = process.platform === 'win32' 
          ? `tasklist /FI "IMAGENAME eq ${params.processName}"` 
          : `pgrep -f ${params.processName}`;
        searchTarget = `process ${params.processName}`;
      } else if (params.port) {
        command = process.platform === 'win32' 
          ? `netstat -an | findstr :${params.port}` 
          : `lsof -i :${params.port}`;
        searchTarget = `port ${params.port}`;
      } else {
        throw new Error('Must specify either processName, pid, or port');
      }

      const { stdout, stderr } = await execAsync(command, { timeout: 10000 });
      const isRunning = stdout.trim().length > 0 && !stdout.includes('No tasks are running');

      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: true,
        output: {
          target: searchTarget,
          isRunning,
          details: stdout.trim(),
          command
        },
        timestamp: new Date(),
        metadata: {
          operation: 'check-process',
          target: searchTarget
        }
      };
    } catch (error) {
      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: false,
        output: null,
        error: `Failed to check process: ${(error as Error).message}`,
        timestamp: new Date()
      };
    }
  }
};

// Export all tools
export {
  executeCommandTool,
  interactiveCommandTool,
  getSystemInfoTool,
  checkProcessTool
};

export default [
  executeCommandTool,
  interactiveCommandTool,
  getSystemInfoTool,
  checkProcessTool
];
