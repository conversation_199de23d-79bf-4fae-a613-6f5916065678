import { z } from 'zod';
import { nanoid } from 'nanoid';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, AgentContext } from '../types';
import { SemanticSearchEngine } from '../core/semantic/SemanticSearchEngine';

// Semantic Search Tool
const semanticSearchSchema = z.object({
  query: z.string().describe('Natural language query to search for in code'),
  limit: z.number().default(10).optional().describe('Maximum number of results to return'),
  fileTypes: z.array(z.string()).optional().describe('File extensions to search in (e.g., ["ts", "js"])')
});

const semanticSearchTool: Tool = {
  name: 'semantic_search',
  description: 'Search for code using natural language queries with semantic understanding',
  schema: semanticSearchSchema,
  category: 'semantic-search',
  async execute(params, context: AgentContext): Promise<ToolResult> {
    try {
      const searchEngine = new SemanticSearchEngine();
      
      // Initialize index if not already done
      if (!searchEngine.hasIndex()) {
        await searchEngine.initializeIndex();
      }

      const results = await searchEngine.search(params.query, params.limit);

      // Filter by file types if specified
      const filteredResults = params.fileTypes 
        ? results.filter(result => {
            const ext = result.filePath.split('.').pop()?.toLowerCase();
            return params.fileTypes!.includes(ext || '');
          })
        : results;

      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: true,
        output: {
          query: params.query,
          results: filteredResults,
          totalResults: filteredResults.length,
          searchStats: searchEngine.getIndexStats()
        },
        timestamp: new Date(),
        metadata: {
          operation: 'semantic-search',
          queryType: 'natural-language',
          resultCount: filteredResults.length
        }
      };
    } catch (error) {
      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: false,
        output: null,
        error: `Semantic search failed: ${(error as Error).message}`,
        timestamp: new Date()
      };
    }
  }
};

// Index File Tool
const indexFileSchema = z.object({
  filePath: z.string().describe('Path to the file to index'),
  content: z.string().optional().describe('File content (if not provided, will read from file)')
});

export const indexFileTool: Tool = {
  name: 'index_file',
  description: 'Index a file for semantic search',
  schema: indexFileSchema,
  category: 'semantic-search',
  async execute(params, context: AgentContext): Promise<ToolResult> {
    try {
      const searchEngine = new SemanticSearchEngine();
      
      let content = params.content;
      if (!content) {
        // Would read file content here
        // For now, return error asking for content
        throw new Error('File content must be provided for indexing');
      }

      await searchEngine.indexFile(params.filePath, content);

      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: true,
        output: {
          filePath: params.filePath,
          indexed: true,
          contentLength: content.length
        },
        timestamp: new Date(),
        metadata: {
          operation: 'index-file',
          filePath: params.filePath
        }
      };
    } catch (error) {
      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: false,
        output: null,
        error: `File indexing failed: ${(error as Error).message}`,
        timestamp: new Date()
      };
    }
  }
};

// Get Index Stats Tool
const getIndexStatsSchema = z.object({
  detailed: z.boolean().default(false).optional().describe('Include detailed statistics')
});

export const getIndexStatsTool: Tool = {
  name: 'get_index_stats',
  description: 'Get statistics about the semantic search index',
  schema: getIndexStatsSchema,
  category: 'semantic-search',
  async execute(params, context: AgentContext): Promise<ToolResult> {
    try {
      const searchEngine = new SemanticSearchEngine();
      const stats = searchEngine.getIndexStats();

      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: true,
        output: {
          stats,
          detailed: params.detailed
        },
        timestamp: new Date(),
        metadata: {
          operation: 'index-stats'
        }
      };
    } catch (error) {
      return {
        id: nanoid(),
        toolCallId: nanoid(),
        success: false,
        output: null,
        error: `Failed to get index stats: ${(error as Error).message}`,
        timestamp: new Date()
      };
    }
  }
};

// Export all tools as default array
export default [
  semanticSearchTool,
  indexFileTool,
  getIndexStatsTool
];
