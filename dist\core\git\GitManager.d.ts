import { EventEmitter } from 'events';
import { GitRepository, GitStatus, GitCommit } from '../../types';
export declare class GitManager extends EventEmitter {
    private git;
    private workingDirectory;
    constructor(workingDirectory: string);
    /**
     * Get repository information
     */
    getRepositoryInfo(): Promise<GitRepository>;
    /**
     * Get repository status
     */
    getStatus(): Promise<GitStatus>;
    /**
     * Get last commit information
     */
    getLastCommit(): Promise<GitCommit | undefined>;
    /**
     * Stage files
     */
    stageFiles(files: string[]): Promise<void>;
    /**
     * Stage all changes
     */
    stageAll(): Promise<void>;
    /**
     * Commit changes
     */
    commit(message: string): Promise<GitCommit>;
    /**
     * Create and switch to new branch
     */
    createBranch(branchName: string, switchTo?: boolean): Promise<void>;
    /**
     * Switch to branch
     */
    switchBranch(branchName: string): Promise<void>;
    /**
     * Get list of branches
     */
    getBranches(): Promise<string[]>;
    /**
     * Pull changes from remote
     */
    pull(remote?: string, branch?: string): Promise<void>;
    /**
     * Push changes to remote
     */
    push(remote?: string, branch?: string): Promise<void>;
    /**
     * Get diff for files
     */
    getDiff(files?: string[]): Promise<string>;
    /**
     * Get diff between commits
     */
    getDiffBetweenCommits(commit1: string, commit2: string): Promise<string>;
    /**
     * Initialize new git repository
     */
    initRepository(): Promise<void>;
    /**
     * Check if directory is a git repository
     */
    isRepository(): Promise<boolean>;
    /**
     * Get commit history
     */
    getCommitHistory(limit?: number): Promise<GitCommit[]>;
    /**
     * Reset changes
     */
    reset(mode?: 'soft' | 'mixed' | 'hard', commit?: string): Promise<void>;
}
//# sourceMappingURL=GitManager.d.ts.map