"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TerminalUI = void 0;
const readline_1 = __importDefault(require("readline"));
const chalk_1 = __importDefault(require("chalk"));
const ora_1 = __importDefault(require("ora"));
const DiffVisualization_1 = require("./DiffVisualization");
class TerminalUI {
    agent;
    rl;
    spinner;
    isProcessing = false;
    constructor(agent) {
        this.agent = agent;
        this.spinner = (0, ora_1.default)();
        this.setupReadline();
        this.setupEventHandlers();
    }
    setupReadline() {
        this.rl = readline_1.default.createInterface({
            input: process.stdin,
            output: process.stdout,
            prompt: chalk_1.default.cyan('🤖 > ')
        });
        this.rl.on('line', async (input) => {
            const trimmedInput = input.trim();
            if (trimmedInput === '') {
                this.rl.prompt();
                return;
            }
            if (trimmedInput === 'exit' || trimmedInput === 'quit') {
                await this.shutdown();
                return;
            }
            if (trimmedInput === 'help') {
                this.showHelp();
                this.rl.prompt();
                return;
            }
            if (trimmedInput === 'status') {
                this.showStatus();
                this.rl.prompt();
                return;
            }
            if (trimmedInput === 'clear') {
                console.clear();
                this.showWelcome();
                this.rl.prompt();
                return;
            }
            if (trimmedInput.startsWith('config ')) {
                await this.handleConfigCommand(trimmedInput.substring(7));
                this.rl.prompt();
                return;
            }
            if (trimmedInput === 'changes') {
                this.showRecentChanges();
                this.rl.prompt();
                return;
            }
            if (trimmedInput.startsWith('diff ')) {
                await this.showDiffCommand(trimmedInput.substring(5));
                this.rl.prompt();
                return;
            }
            // Process user input
            await this.processUserInput(trimmedInput);
        });
        this.rl.on('close', () => {
            this.shutdown();
        });
    }
    setupEventHandlers() {
        // Processing events
        this.agent.on('processing-started', () => {
            this.isProcessing = true;
            this.spinner.start(chalk_1.default.blue('🧠 Agent is thinking...'));
        });
        this.agent.on('processing-completed', () => {
            this.isProcessing = false;
            this.spinner.stop();
            console.log(chalk_1.default.green('✓ Task completed\n'));
            this.rl.prompt();
        });
        // Tool execution events
        this.agent.on('tool-execution-started', (toolCall) => {
            this.spinner.text = chalk_1.default.yellow(`🔧 Executing: ${toolCall.name}`);
        });
        this.agent.on('tool-execution-completed', (result) => {
            if (result.success) {
                console.log(chalk_1.default.green(`✓ ${result.metadata?.toolName || 'Tool'} completed`));
                // Show relevant output
                if (result.output && typeof result.output === 'object') {
                    this.displayToolOutput(result);
                }
            }
        });
        this.agent.on('tool-execution-failed', (result) => {
            console.log(chalk_1.default.red(`✗ ${result.metadata?.toolName || 'Tool'} failed: ${result.error}`));
        });
        // Change tracking events
        this.agent.on('change-tracked', (change) => {
            this.displayChange(change);
        });
        // Git events
        this.agent.on('git-operation', (operation) => {
            console.log(chalk_1.default.magenta(`🔀 Git: ${operation.type} - ${operation.message || 'Operation completed'}`));
        });
        // Error events
        this.agent.on('error', (error) => {
            this.spinner.stop();
            console.log(chalk_1.default.red(`❌ Error: ${error.message}`));
            this.isProcessing = false;
            this.rl.prompt();
        });
        this.agent.on('fatal-error', (error) => {
            this.spinner.stop();
            console.log(chalk_1.default.red(`💥 Fatal Error: ${error.message}`));
            console.log(chalk_1.default.yellow('Agent is shutting down...'));
            this.shutdown();
        });
    }
    async start() {
        this.showWelcome();
        this.rl.prompt();
    }
    showWelcome() {
        console.log(chalk_1.default.blue.bold('\n🤖 Autonomous Agent CLI'));
        console.log(chalk_1.default.gray('Type your request and I\'ll execute it autonomously.'));
        console.log(chalk_1.default.gray('Commands: help, status, clear, config, exit\n'));
    }
    showHelp() {
        console.log(chalk_1.default.blue('\n📖 Available Commands:'));
        console.log(chalk_1.default.white('  help       - Show this help message'));
        console.log(chalk_1.default.white('  status     - Show agent status and context'));
        console.log(chalk_1.default.white('  changes    - Show recent changes with diffs'));
        console.log(chalk_1.default.white('  diff <id>  - Show detailed diff for a change'));
        console.log(chalk_1.default.white('  clear      - Clear the terminal'));
        console.log(chalk_1.default.white('  config     - Show configuration'));
        console.log(chalk_1.default.white('  exit       - Exit the agent\n'));
        console.log(chalk_1.default.blue('💡 Example Requests:'));
        console.log(chalk_1.default.white('  "Create a new React component called Button"'));
        console.log(chalk_1.default.white('  "Fix the authentication bug in login.js"'));
        console.log(chalk_1.default.white('  "Search for TODO comments in the codebase"'));
        console.log(chalk_1.default.white('  "Run the test suite and fix any failures"'));
        console.log(chalk_1.default.white('  "Commit all changes with a descriptive message"\n'));
    }
    showStatus() {
        const context = this.agent.getContext();
        console.log(chalk_1.default.blue('\n📊 Agent Status:'));
        console.log(chalk_1.default.white(`  Session ID: ${context.sessionId}`));
        console.log(chalk_1.default.white(`  Working Directory: ${context.workingDirectory}`));
        console.log(chalk_1.default.white(`  Git Repository: ${context.gitRepository ? 'Yes' : 'No'}`));
        console.log(chalk_1.default.white(`  Active Tools: ${context.activeTools.length}`));
        console.log(chalk_1.default.white(`  Conversation History: ${context.conversationHistory.length} messages`));
        console.log(chalk_1.default.white(`  Changes Tracked: ${context.changeHistory.length}`));
        console.log(chalk_1.default.white(`  Processing: ${this.isProcessing ? 'Yes' : 'No'}\n`));
    }
    async handleConfigCommand(command) {
        if (command === 'show') {
            // Show current configuration (without sensitive data)
            console.log(chalk_1.default.blue('\n⚙️ Current Configuration:'));
            console.log(chalk_1.default.white('  Provider: OpenAI')); // This would come from actual config
            console.log(chalk_1.default.white('  Model: gpt-4-turbo-preview'));
            console.log(chalk_1.default.white('  Temperature: 0.7\n'));
        }
        else {
            console.log(chalk_1.default.yellow('Available config commands: show\n'));
        }
    }
    async processUserInput(input) {
        try {
            await this.agent.processInput(input);
        }
        catch (error) {
            this.spinner.stop();
            console.log(chalk_1.default.red(`Error processing input: ${error.message}`));
            this.isProcessing = false;
            this.rl.prompt();
        }
    }
    displayToolOutput(result) {
        const output = result.output;
        const toolName = result.metadata?.toolName;
        switch (toolName) {
            case 'read_file':
                console.log(chalk_1.default.gray(`📄 Read ${output.path} (${output.size} bytes)`));
                break;
            case 'write_file':
                console.log(chalk_1.default.gray(`💾 ${output.created ? 'Created' : 'Modified'} ${output.path} (${output.bytesWritten} bytes)`));
                break;
            case 'execute_command':
                console.log(chalk_1.default.gray(`⚡ Command: ${output.command}`));
                if (output.stdout) {
                    console.log(chalk_1.default.white(output.stdout));
                }
                if (output.stderr) {
                    console.log(chalk_1.default.red(output.stderr));
                }
                break;
            case 'list_directory':
                console.log(chalk_1.default.gray(`📁 Listed ${output.path} (${output.count} items)`));
                if (output.items && output.items.length <= 10) {
                    output.items.forEach((item) => {
                        const icon = item.type === 'directory' ? '📁' : '📄';
                        console.log(chalk_1.default.white(`  ${icon} ${item.name}`));
                    });
                }
                break;
            case 'search_files':
                console.log(chalk_1.default.gray(`🔍 Found ${output.totalMatches} matches for "${output.pattern}"`));
                if (output.results && output.results.length <= 5) {
                    output.results.forEach((match) => {
                        console.log(chalk_1.default.white(`  ${match.file}:${match.line} - ${match.content}`));
                    });
                }
                break;
            default:
                // Generic output display
                if (typeof output === 'string') {
                    console.log(chalk_1.default.white(output));
                }
        }
    }
    displayChange(change) {
        const icons = {
            'file-create': '📝',
            'file-modify': '✏️',
            'file-delete': '🗑️',
            'git-operation': '🔀'
        };
        const icon = icons[change.type] || '📄';
        console.log(chalk_1.default.cyan(`${icon} ${change.type}: ${change.path}`));
        // Show enhanced diff visualization if available
        if (change.diff) {
            const compactDiff = DiffVisualization_1.DiffVisualization.renderCompact(change.diff, change.path, {
                colorize: true,
                showStats: true
            });
            console.log(`  ${compactDiff}`);
            // Show detailed diff for file operations (limited lines)
            if (change.type.startsWith('file-') && change.diff.hunks.length > 0) {
                const detailedDiff = DiffVisualization_1.DiffVisualization.renderDiff(change.diff, change.path, {
                    maxLines: 10,
                    showLineNumbers: false,
                    colorize: true,
                    compact: true
                });
                console.log(chalk_1.default.dim('  Preview:'));
                console.log(detailedDiff.split('\n').map(line => `    ${line}`).join('\n'));
            }
        }
    }
    showRecentChanges() {
        const context = this.agent.getContext();
        const recentChanges = context.changeHistory.slice(-10); // Last 10 changes
        if (recentChanges.length === 0) {
            console.log(chalk_1.default.yellow('\n📋 No changes tracked yet\n'));
            return;
        }
        console.log(chalk_1.default.blue(`\n📋 Recent Changes (${recentChanges.length}/${context.changeHistory.length}):`));
        console.log(chalk_1.default.gray('─'.repeat(60)));
        const changesSummary = DiffVisualization_1.DiffVisualization.renderChangesSummary(recentChanges, {
            colorize: true,
            showStats: true,
            maxLines: 20
        });
        console.log(changesSummary);
        console.log(chalk_1.default.gray('\nUse "diff <change-id>" to see detailed diff\n'));
    }
    async showDiffCommand(changeId) {
        const context = this.agent.getContext();
        const change = context.changeHistory.find(c => c.id.startsWith(changeId));
        if (!change) {
            console.log(chalk_1.default.red(`\n❌ Change with ID "${changeId}" not found\n`));
            return;
        }
        if (!change.diff) {
            console.log(chalk_1.default.yellow(`\n⚠️ No diff available for change "${changeId}"\n`));
            return;
        }
        console.log(chalk_1.default.blue(`\n📄 Detailed Diff for ${change.path}:`));
        console.log(chalk_1.default.gray('─'.repeat(60)));
        const detailedDiff = DiffVisualization_1.DiffVisualization.renderDiff(change.diff, change.path, {
            colorize: true,
            showLineNumbers: true,
            showStats: true,
            maxLines: 100
        });
        console.log(detailedDiff);
        console.log(chalk_1.default.gray('\n─'.repeat(60)));
        console.log(chalk_1.default.dim(`Change ID: ${change.id} | Type: ${change.type} | Time: ${change.timestamp.toLocaleString()}\n`));
    }
    async shutdown() {
        this.spinner.stop();
        console.log(chalk_1.default.yellow('\n👋 Shutting down agent...'));
        try {
            await this.agent.shutdown();
            console.log(chalk_1.default.green('✓ Agent shutdown complete'));
        }
        catch (error) {
            console.log(chalk_1.default.red(`Error during shutdown: ${error.message}`));
        }
        this.rl.close();
        process.exit(0);
    }
}
exports.TerminalUI = TerminalUI;
//# sourceMappingURL=TerminalUI.js.map