import { EventEmitter } from 'events';
import { AgentConfig, AgentContext } from '../types';
/**
 * Central orchestrator that manages the entire agent workflow
 * Coordinates all components and maintains conversation state
 */
export declare class AgentEngine extends EventEmitter {
    private config;
    private aiProvider;
    private toolRegistry;
    private contextManager;
    private diffTracker;
    private semanticSearch;
    private gitManager;
    private context;
    private isProcessing;
    constructor(config: AgentConfig, workingDirectory: string);
    private initializeComponents;
    private setupEventHandlers;
    private registerCoreTools;
    /**
     * Process user input and execute autonomous workflow
     */
    processInput(input: string): Promise<void>;
    /**
     * Autonomous execution loop - continues until task completion
     */
    private autonomousExecutionLoop;
    /**
     * Execute multiple tool calls in sequence or parallel
     */
    private executeToolCalls;
    /**
     * Determine if execution should continue based on tool results
     */
    private shouldContinueExecution;
    /**
     * Handle tool execution results
     */
    private handleToolResult;
    /**
     * Handle errors with recovery strategies
     */
    private handleError;
    /**
     * Get current agent context
     */
    getContext(): AgentContext;
    /**
     * Update agent configuration
     */
    updateConfig(newConfig: Partial<AgentConfig>): Promise<void>;
    /**
     * Reset agent state
     */
    reset(): Promise<void>;
    /**
     * Graceful shutdown
     */
    shutdown(): Promise<void>;
}
//# sourceMappingURL=AgentEngine.d.ts.map