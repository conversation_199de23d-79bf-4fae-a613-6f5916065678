{"version": 3, "file": "GitOperations.js", "sourceRoot": "", "sources": ["../../src/tools/GitOperations.ts"], "names": [], "mappings": ";;;AAAA,6BAAwB;AACxB,mCAAgC;AAEhC,uDAAoD;AAEpD,kBAAkB;AAClB,MAAM,eAAe,GAAG,OAAC,CAAC,MAAM,CAAC;IAC/B,QAAQ,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,qCAAqC,CAAC;CAChG,CAAC,CAAC;AAEU,QAAA,aAAa,GAAS;IACjC,IAAI,EAAE,YAAY;IAClB,WAAW,EAAE,uCAAuC;IACpD,MAAM,EAAE,eAAe;IACvB,QAAQ,EAAE,gBAAgB;IAC1B,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,OAAqB;QACzC,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,uBAAU,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;YAE5D,IAAI,CAAC,MAAM,UAAU,CAAC,YAAY,EAAE,EAAE,CAAC;gBACrC,OAAO;oBACL,EAAE,EAAE,IAAA,eAAM,GAAE;oBACZ,UAAU,EAAE,IAAA,eAAM,GAAE;oBACpB,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,IAAI;oBACZ,KAAK,EAAE,sBAAsB;oBAC7B,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC;YACJ,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,SAAS,EAAE,CAAC;YAC5C,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,UAAU,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;YAE/E,OAAO;gBACL,EAAE,EAAE,IAAA,eAAM,GAAE;gBACZ,UAAU,EAAE,IAAA,eAAM,GAAE;gBACpB,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE;oBACN,MAAM;oBACN,GAAG,CAAC,MAAM,CAAC,QAAQ,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC;iBACjD;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,QAAQ,EAAE;oBACR,SAAS,EAAE,QAAQ;oBACnB,UAAU,EAAE,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC;iBAC/F;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,EAAE,EAAE,IAAA,eAAM,GAAE;gBACZ,UAAU,EAAE,IAAA,eAAM,GAAE;gBACpB,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,IAAI;gBACZ,KAAK,EAAE,sBAAuB,KAAe,CAAC,OAAO,EAAE;gBACvD,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAC;AAEF,eAAe;AACf,MAAM,YAAY,GAAG,OAAC,CAAC,MAAM,CAAC;IAC5B,KAAK,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,+CAA+C,CAAC;IAC/F,GAAG,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,uCAAuC,CAAC;CAC7F,CAAC,CAAC;AAEU,QAAA,UAAU,GAAS;IAC9B,IAAI,EAAE,SAAS;IACf,WAAW,EAAE,wBAAwB;IACrC,MAAM,EAAE,YAAY;IACpB,QAAQ,EAAE,gBAAgB;IAC1B,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,OAAqB;QACzC,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,uBAAU,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;YAE5D,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC7D,MAAM,UAAU,CAAC,QAAQ,EAAE,CAAC;YAC9B,CAAC;iBAAM,CAAC;gBACN,MAAM,UAAU,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5C,CAAC;YAED,MAAM,YAAY,GAAiB;gBACjC,EAAE,EAAE,IAAA,eAAM,GAAE;gBACZ,IAAI,EAAE,eAAe;gBACrB,IAAI,EAAE,aAAa;gBACnB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,QAAQ,EAAE;oBACR,SAAS,EAAE,OAAO;oBAClB,KAAK,EAAE,MAAM,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC;iBAC/B;aACF,CAAC;YAEF,OAAO;gBACL,EAAE,EAAE,IAAA,eAAM,GAAE;gBACZ,UAAU,EAAE,IAAA,eAAM,GAAE;gBACpB,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE;oBACN,MAAM,EAAE,MAAM,CAAC,KAAK,IAAI,aAAa;oBACrC,SAAS,EAAE,OAAO;iBACnB;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,QAAQ,EAAE;oBACR,SAAS,EAAE,OAAO;oBAClB,WAAW,EAAE,CAAC,YAAY,CAAC;iBAC5B;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,EAAE,EAAE,IAAA,eAAM,GAAE;gBACZ,UAAU,EAAE,IAAA,eAAM,GAAE;gBACpB,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,IAAI;gBACZ,KAAK,EAAE,mBAAoB,KAAe,CAAC,OAAO,EAAE;gBACpD,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAC;AAEF,kBAAkB;AAClB,MAAM,eAAe,GAAG,OAAC,CAAC,MAAM,CAAC;IAC/B,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,gBAAgB,CAAC;IAC9C,MAAM,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,qCAAqC,CAAC;CAC9F,CAAC,CAAC;AAEU,QAAA,aAAa,GAAS;IACjC,IAAI,EAAE,YAAY;IAClB,WAAW,EAAE,uBAAuB;IACpC,MAAM,EAAE,eAAe;IACvB,QAAQ,EAAE,gBAAgB;IAC1B,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,OAAqB;QACzC,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,uBAAU,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;YAE5D,iCAAiC;YACjC,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;gBAClB,MAAM,UAAU,CAAC,QAAQ,EAAE,CAAC;YAC9B,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YAEvD,MAAM,YAAY,GAAiB;gBACjC,EAAE,EAAE,IAAA,eAAM,GAAE;gBACZ,IAAI,EAAE,eAAe;gBACrB,IAAI,EAAE,YAAY;gBAClB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,QAAQ,EAAE;oBACR,SAAS,EAAE,QAAQ;oBACnB,MAAM,EAAE,MAAM;iBACf;aACF,CAAC;YAEF,OAAO;gBACL,EAAE,EAAE,IAAA,eAAM,GAAE;gBACZ,UAAU,EAAE,IAAA,eAAM,GAAE;gBACpB,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE;oBACN,MAAM;oBACN,OAAO,EAAE,MAAM,CAAC,OAAO;iBACxB;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,QAAQ,EAAE;oBACR,SAAS,EAAE,QAAQ;oBACnB,WAAW,EAAE,CAAC,YAAY,CAAC;iBAC5B;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,EAAE,EAAE,IAAA,eAAM,GAAE;gBACZ,UAAU,EAAE,IAAA,eAAM,GAAE;gBACpB,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,IAAI;gBACZ,KAAK,EAAE,sBAAuB,KAAe,CAAC,OAAO,EAAE;gBACvD,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAC;AAEF,kBAAkB;AAClB,MAAM,eAAe,GAAG,OAAC,CAAC,MAAM,CAAC;IAC/B,MAAM,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,0BAA0B,CAAC;IAC3F,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,mDAAmD,CAAC;IAC/F,QAAQ,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,iCAAiC,CAAC;CAC3F,CAAC,CAAC;AAEU,QAAA,aAAa,GAAS;IACjC,IAAI,EAAE,YAAY;IAClB,WAAW,EAAE,qBAAqB;IAClC,MAAM,EAAE,eAAe;IACvB,QAAQ,EAAE,gBAAgB;IAC1B,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,OAAqB;QACzC,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,uBAAU,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;YAE5D,IAAI,MAAM,GAAQ,EAAE,CAAC;YAErB,QAAQ,MAAM,CAAC,MAAM,EAAE,CAAC;gBACtB,KAAK,MAAM;oBACT,MAAM,QAAQ,GAAG,MAAM,UAAU,CAAC,WAAW,EAAE,CAAC;oBAChD,MAAM,GAAG,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,aAAa,EAAE,aAAa,EAAE,CAAC;oBACrE,MAAM;gBAER,KAAK,QAAQ;oBACX,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;wBACvB,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;oBAC/D,CAAC;oBACD,MAAM,UAAU,CAAC,YAAY,CAAC,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;oBAClE,MAAM,GAAG,EAAE,OAAO,EAAE,MAAM,CAAC,UAAU,EAAE,UAAU,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC;oBACrE,MAAM;gBAER,KAAK,QAAQ;oBACX,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;wBACvB,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;oBAC/D,CAAC;oBACD,MAAM,UAAU,CAAC,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;oBACjD,MAAM,GAAG,EAAE,UAAU,EAAE,MAAM,CAAC,UAAU,EAAE,CAAC;oBAC3C,MAAM;gBAER,KAAK,QAAQ;oBACX,4EAA4E;oBAC5E,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;gBAEjE;oBACE,MAAM,IAAI,KAAK,CAAC,0BAA0B,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;YAC/D,CAAC;YAED,OAAO;gBACL,EAAE,EAAE,IAAA,eAAM,GAAE;gBACZ,UAAU,EAAE,IAAA,eAAM,GAAE;gBACpB,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE;oBACN,MAAM,EAAE,MAAM,CAAC,MAAM;oBACrB,GAAG,MAAM;iBACV;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,QAAQ,EAAE;oBACR,SAAS,EAAE,UAAU,MAAM,CAAC,MAAM,EAAE;oBACpC,UAAU,EAAE,MAAM,CAAC,UAAU;iBAC9B;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,EAAE,EAAE,IAAA,eAAM,GAAE;gBACZ,UAAU,EAAE,IAAA,eAAM,GAAE;gBACpB,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,IAAI;gBACZ,KAAK,EAAE,gCAAiC,KAAe,CAAC,OAAO,EAAE;gBACjE,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAC;AAEF,gBAAgB;AAChB,MAAM,aAAa,GAAG,OAAC,CAAC,MAAM,CAAC;IAC7B,KAAK,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,wBAAwB,CAAC;IACxE,MAAM,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,6BAA6B,CAAC;IACrF,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,6BAA6B,CAAC;IACtE,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,8BAA8B,CAAC;CACxE,CAAC,CAAC;AAEU,QAAA,WAAW,GAAS;IAC/B,IAAI,EAAE,UAAU;IAChB,WAAW,EAAE,oCAAoC;IACjD,MAAM,EAAE,aAAa;IACrB,QAAQ,EAAE,gBAAgB;IAC1B,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,OAAqB;QACzC,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,uBAAU,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;YAC5D,IAAI,IAAY,CAAC;YAEjB,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACrC,IAAI,GAAG,MAAM,UAAU,CAAC,qBAAqB,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;YAChF,CAAC;iBAAM,CAAC;gBACN,IAAI,GAAG,MAAM,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAChD,CAAC;YAED,OAAO;gBACL,EAAE,EAAE,IAAA,eAAM,GAAE;gBACZ,UAAU,EAAE,IAAA,eAAM,GAAE;gBACpB,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE;oBACN,IAAI;oBACJ,KAAK,EAAE,MAAM,CAAC,KAAK;oBACnB,MAAM,EAAE,MAAM,CAAC,MAAM;oBACrB,OAAO,EAAE,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS;iBACzF;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,QAAQ,EAAE;oBACR,SAAS,EAAE,MAAM;oBACjB,UAAU,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC;iBAC5B;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,EAAE,EAAE,IAAA,eAAM,GAAE;gBACZ,UAAU,EAAE,IAAA,eAAM,GAAE;gBACpB,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,IAAI;gBACZ,KAAK,EAAE,oBAAqB,KAAe,CAAC,OAAO,EAAE;gBACrD,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAC;AAEF,eAAe;AACf,MAAM,YAAY,GAAG,OAAC,CAAC,MAAM,CAAC;IAC5B,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,2BAA2B,CAAC;IAC9E,OAAO,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,0BAA0B,CAAC;CACpF,CAAC,CAAC;AAEU,QAAA,UAAU,GAAS;IAC9B,IAAI,EAAE,SAAS;IACf,WAAW,EAAE,yBAAyB;IACtC,MAAM,EAAE,YAAY;IACpB,QAAQ,EAAE,gBAAgB;IAC1B,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,OAAqB;QACzC,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,IAAI,uBAAU,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;YAC5D,MAAM,OAAO,GAAG,MAAM,UAAU,CAAC,gBAAgB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAEhE,OAAO;gBACL,EAAE,EAAE,IAAA,eAAM,GAAE;gBACZ,UAAU,EAAE,IAAA,eAAM,GAAE;gBACpB,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE;oBACN,OAAO;oBACP,KAAK,EAAE,OAAO,CAAC,MAAM;oBACrB,KAAK,EAAE,MAAM,CAAC,KAAK;iBACpB;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,QAAQ,EAAE;oBACR,SAAS,EAAE,KAAK;oBAChB,WAAW,EAAE,OAAO,CAAC,MAAM;iBAC5B;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,EAAE,EAAE,IAAA,eAAM,GAAE;gBACZ,UAAU,EAAE,IAAA,eAAM,GAAE;gBACpB,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,IAAI;gBACZ,KAAK,EAAE,mBAAoB,KAAe,CAAC,OAAO,EAAE;gBACpD,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAC;AAYF,kBAAe;IACb,qBAAa;IACb,kBAAU;IACV,qBAAa;IACb,qBAAa;IACb,mBAAW;IACX,kBAAU;CACX,CAAC"}