"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIProvider = void 0;
const openai_1 = __importDefault(require("openai"));
/**
 * Universal AI Provider using OpenAI SDK 5.0.0
 * Supports OpenAI, Deepseek, and Ollama through compatible APIs
 */
class AIProvider {
    client;
    config;
    constructor(config) {
        this.config = config;
        this.initializeClient();
    }
    initializeClient() {
        const clientConfig = {
            apiKey: this.config.apiKey,
        };
        // Configure provider-specific endpoints
        switch (this.config.provider) {
            case 'openai':
                // Default OpenAI configuration
                break;
            case 'deepseek':
                clientConfig.baseURL = this.config.baseUrl || 'https://api.deepseek.com/v1';
                break;
            case 'ollama':
                clientConfig.baseURL = this.config.baseUrl || 'http://localhost:11434/v1';
                clientConfig.apiKey = 'ollama'; // Ollama doesn't require real API key
                break;
            default:
                throw new Error(`Unsupported AI provider: ${this.config.provider}`);
        }
        this.client = new openai_1.default(clientConfig);
    }
    /**
     * Get AI response with streaming support and tool calls
     */
    async getResponse(conversationHistory, toolSchemas, context) {
        try {
            const messages = this.formatMessages(conversationHistory, context);
            const tools = this.formatToolSchemas(toolSchemas);
            const completion = await this.client.chat.completions.create({
                model: this.config.model,
                messages,
                tools: tools.length > 0 ? tools : undefined,
                tool_choice: tools.length > 0 ? 'auto' : undefined,
                temperature: this.config.temperature || 0.7,
                max_tokens: this.config.maxTokens || 4000,
                stream: false, // We'll implement streaming separately
            });
            return this.parseResponse(completion);
        }
        catch (error) {
            throw new Error(`AI Provider Error: ${error.message}`);
        }
    }
    /**
     * Get streaming AI response
     */
    async getStreamingResponse(conversationHistory, toolSchemas, context, onChunk, onToolCall) {
        try {
            const messages = this.formatMessages(conversationHistory, context);
            const tools = this.formatToolSchemas(toolSchemas);
            const stream = await this.client.chat.completions.create({
                model: this.config.model,
                messages,
                tools: tools.length > 0 ? tools : undefined,
                tool_choice: tools.length > 0 ? 'auto' : undefined,
                temperature: this.config.temperature || 0.7,
                max_tokens: this.config.maxTokens || 4000,
                stream: true,
            });
            let content = '';
            let toolCalls = [];
            let usage = undefined;
            for await (const chunk of stream) {
                const delta = chunk.choices[0]?.delta;
                if (delta?.content) {
                    content += delta.content;
                    onChunk(delta.content);
                }
                if (delta?.tool_calls) {
                    for (const toolCall of delta.tool_calls) {
                        if (toolCall.function) {
                            const parsedToolCall = {
                                id: toolCall.id || `tool_${Date.now()}`,
                                name: toolCall.function.name || 'unknown',
                                parameters: JSON.parse(toolCall.function.arguments || '{}'),
                                timestamp: new Date()
                            };
                            toolCalls.push(parsedToolCall);
                            onToolCall(parsedToolCall);
                        }
                    }
                }
                if (chunk.usage) {
                    usage = chunk.usage;
                }
            }
            return {
                content,
                toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
                usage: usage ? {
                    promptTokens: usage.prompt_tokens,
                    completionTokens: usage.completion_tokens,
                    totalTokens: usage.total_tokens
                } : undefined
            };
        }
        catch (error) {
            throw new Error(`AI Streaming Error: ${error.message}`);
        }
    }
    /**
     * Format conversation messages for AI provider
     */
    formatMessages(conversationHistory, context) {
        const systemMessage = {
            role: 'system',
            content: this.buildSystemPrompt(context)
        };
        const messages = [systemMessage];
        for (const msg of conversationHistory) {
            if (msg.role === 'user') {
                messages.push({
                    role: 'user',
                    content: msg.content
                });
            }
            else if (msg.role === 'assistant') {
                const assistantMessage = {
                    role: 'assistant',
                    content: msg.content
                };
                if (msg.toolCalls && msg.toolCalls.length > 0) {
                    assistantMessage.tool_calls = msg.toolCalls.map(tc => ({
                        id: tc.id,
                        type: 'function',
                        function: {
                            name: tc.name,
                            arguments: JSON.stringify(tc.parameters)
                        }
                    }));
                }
                messages.push(assistantMessage);
                // Add tool results as tool messages
                if (msg.toolResults && msg.toolResults.length > 0) {
                    for (const result of msg.toolResults) {
                        messages.push({
                            role: 'tool',
                            tool_call_id: result.toolCallId,
                            content: JSON.stringify({
                                success: result.success,
                                output: result.output,
                                error: result.error,
                                metadata: result.metadata
                            })
                        });
                    }
                }
            }
        }
        return messages;
    }
    /**
     * Build comprehensive system prompt with context
     */
    buildSystemPrompt(context) {
        return `You are an autonomous AI agent with full access to the user's development environment. You operate without approval requirements and execute all necessary actions to complete tasks.

CURRENT CONTEXT:
- Working Directory: ${context.workingDirectory}
- Session ID: ${context.sessionId}
- Git Repository: ${context.gitRepository ? 'Yes' : 'No'}
- Active Tools: ${context.activeTools.join(', ')}
- Change History: ${context.changeHistory.length} changes tracked

CAPABILITIES:
- File operations (read, write, create, delete, search)
- Shell command execution with full system access
- Git operations (commit, branch, merge, diff)
- Semantic code search and understanding
- Real-time debugging and error resolution
- Autonomous decision making and task completion

BEHAVIOR:
- Execute all tool calls immediately without asking for permission
- Continue working until the task is fully completed
- Track all changes with detailed diffs
- Maintain context across operations
- Handle errors gracefully with recovery strategies
- Provide clear explanations of actions taken

TOOLS AVAILABLE:
Use the provided tools to accomplish any task. All tool calls will be executed automatically.

Remember: You have full autonomy. Execute whatever actions are necessary to complete the user's request efficiently and effectively.`;
    }
    /**
     * Format tool schemas for AI provider
     */
    formatToolSchemas(toolSchemas) {
        return toolSchemas.map(schema => ({
            type: 'function',
            function: {
                name: schema.name,
                description: schema.description,
                parameters: schema.parameters
            }
        }));
    }
    /**
     * Parse AI response and extract tool calls
     */
    parseResponse(completion) {
        const choice = completion.choices[0];
        const message = choice.message;
        let toolCalls;
        if (message.tool_calls && message.tool_calls.length > 0) {
            toolCalls = message.tool_calls.map(tc => ({
                id: tc.id,
                name: tc.function.name,
                parameters: JSON.parse(tc.function.arguments),
                timestamp: new Date()
            }));
        }
        return {
            content: message.content || '',
            toolCalls,
            usage: completion.usage ? {
                promptTokens: completion.usage.prompt_tokens,
                completionTokens: completion.usage.completion_tokens,
                totalTokens: completion.usage.total_tokens
            } : undefined
        };
    }
    /**
     * Update provider configuration
     */
    updateConfig(newConfig) {
        this.config = newConfig;
        this.initializeClient();
    }
    /**
     * Test connection to AI provider
     */
    async testConnection() {
        try {
            await this.client.chat.completions.create({
                model: this.config.model,
                messages: [{ role: 'user', content: 'Test connection' }],
                max_tokens: 10
            });
            return true;
        }
        catch (error) {
            return false;
        }
    }
}
exports.AIProvider = AIProvider;
//# sourceMappingURL=AIProvider.js.map