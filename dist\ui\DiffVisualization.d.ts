import { DiffResult, ChangeRecord } from '../types';
export interface DiffDisplayOptions {
    maxLines?: number;
    showLineNumbers?: boolean;
    showStats?: boolean;
    colorize?: boolean;
    contextLines?: number;
    showHeader?: boolean;
    compact?: boolean;
}
export declare class DiffVisualization {
    private static readonly DEFAULT_OPTIONS;
    /**
     * Render a diff for terminal display
     */
    static renderDiff(diff: DiffResult, filePath: string, options?: DiffDisplayOptions): string;
    /**
     * Render diff header
     */
    private static renderHeader;
    /**
     * Render diff statistics
     */
    private static renderStats;
    /**
     * Render a single diff hunk
     */
    private static renderHunk;
    /**
     * Render a single diff line
     */
    private static renderDiffLine;
    /**
     * Render multiple changes as a summary
     */
    static renderChangesSummary(changes: ChangeRecord[], options?: DiffDisplayOptions): string;
    /**
     * Render side-by-side diff comparison
     */
    static renderSideBySide(diff: DiffResult, filePath: string, options?: DiffDisplayOptions): string;
    /**
     * Render compact diff (one-line summary)
     */
    static renderCompact(diff: DiffResult, filePath: string, options?: DiffDisplayOptions): string;
    /**
     * Get icon for change type
     */
    private static getChangeTypeIcon;
    /**
     * Apply color if colorization is enabled
     */
    private static colorize;
    /**
     * Create a diff visualization for terminal display
     */
    static createTerminalDiff(oldContent: string, newContent: string, filePath: string, options?: DiffDisplayOptions): string;
    /**
     * Format diff for streaming output
     */
    static formatForStreaming(diff: DiffResult, filePath: string): string;
    /**
     * Get terminal width for responsive rendering
     */
    static getTerminalWidth(): number;
    /**
     * Strip ANSI colors for plain text output
     */
    static stripColors(text: string): string;
}
//# sourceMappingURL=DiffVisualization.d.ts.map