{"version": 3, "file": "ShellCommands.js", "sourceRoot": "", "sources": ["../../src/tools/ShellCommands.ts"], "names": [], "mappings": ";;;AAAA,iDAA4C;AAC5C,+BAAiC;AACjC,6BAAwB;AACxB,mCAAgC;AAGhC,MAAM,SAAS,GAAG,IAAA,gBAAS,EAAC,oBAAI,CAAC,CAAC;AAElC,6BAA6B;AAC7B,MAAM,oBAAoB,GAAG,OAAC,CAAC,MAAM,CAAC;IACpC,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,0BAA0B,CAAC;IACxD,IAAI,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,mBAAmB,CAAC;IAClE,GAAG,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,yCAAyC,CAAC;IAC9E,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,yBAAyB,CAAC;IACjF,GAAG,EAAE,OAAC,CAAC,MAAM,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,uBAAuB,CAAC;IACtE,KAAK,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,kBAAkB,CAAC;CACzE,CAAC,CAAC;AAEH,MAAM,kBAAkB,GAAS;IAC/B,IAAI,EAAE,iBAAiB;IACvB,WAAW,EAAE,+CAA+C;IAC5D,MAAM,EAAE,oBAAoB;IAC5B,QAAQ,EAAE,gBAAgB;IAC1B,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,OAAqB;QACzC,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,CAAC,GAAG;gBAC3B,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,gBAAgB,IAAI,MAAM,CAAC,GAAG,EAAE,CAAC;gBACzF,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC;YAE7B,MAAM,WAAW,GAAG,MAAM,CAAC,IAAI;gBAC7B,CAAC,CAAC,GAAG,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;gBAC9C,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC;YAEnB,MAAM,OAAO,GAAG;gBACd,GAAG,EAAE,UAAU;gBACf,OAAO,EAAE,MAAM,CAAC,OAAO,IAAI,KAAK;gBAChC,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,GAAG,EAAE;gBACtC,KAAK,EAAE,MAAM,CAAC,KAAK,KAAK,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW;aACnD,CAAC;YAEF,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,SAAS,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;YAEjE,OAAO;gBACL,EAAE,EAAE,IAAA,eAAM,GAAE;gBACZ,UAAU,EAAE,IAAA,eAAM,GAAE;gBACpB,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE;oBACN,OAAO,EAAE,WAAW;oBACpB,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE;oBACrB,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE;oBACrB,QAAQ,EAAE,CAAC;oBACX,gBAAgB,EAAE,UAAU;iBAC7B;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,QAAQ,EAAE;oBACR,SAAS,EAAE,SAAS;oBACpB,OAAO,EAAE,WAAW;oBACpB,aAAa,EAAE,IAAI,CAAC,GAAG,EAAE;iBAC1B;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO;gBACL,EAAE,EAAE,IAAA,eAAM,GAAE;gBACZ,UAAU,EAAE,IAAA,eAAM,GAAE;gBACpB,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE;oBACN,OAAO,EAAE,MAAM,CAAC,OAAO;oBACvB,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,EAAE;oBAC1B,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO;oBACrC,QAAQ,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC;oBACzB,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;iBAC3C;gBACD,KAAK,EAAE,mBAAmB,KAAK,CAAC,OAAO,EAAE;gBACzC,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAC;AAwQA,gDAAkB;AAtQpB,iCAAiC;AACjC,MAAM,wBAAwB,GAAG,OAAC,CAAC,MAAM,CAAC;IACxC,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,sCAAsC,CAAC;IACpE,MAAM,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,2CAA2C,CAAC;IAC5F,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,yBAAyB,CAAC;IACjF,GAAG,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,mBAAmB,CAAC;CACzD,CAAC,CAAC;AAEH,MAAM,sBAAsB,GAAS;IACnC,IAAI,EAAE,qBAAqB;IAC3B,WAAW,EAAE,2DAA2D;IACxE,MAAM,EAAE,wBAAwB;IAChC,QAAQ,EAAE,gBAAgB;IAC1B,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,OAAqB;QACzC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,IAAI,CAAC;gBACH,MAAM,UAAU,GAAG,MAAM,CAAC,GAAG;oBAC3B,CAAC,CAAC,GAAG,OAAO,CAAC,gBAAgB,IAAI,MAAM,CAAC,GAAG,EAAE;oBAC7C,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC;gBAE7B,MAAM,KAAK,GAAG,IAAA,qBAAK,EAAC,MAAM,CAAC,OAAO,EAAE,EAAE,EAAE;oBACtC,GAAG,EAAE,UAAU;oBACf,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;oBAC/B,KAAK,EAAE,IAAI;iBACZ,CAAC,CAAC;gBAEH,IAAI,MAAM,GAAG,EAAE,CAAC;gBAChB,IAAI,MAAM,GAAG,EAAE,CAAC;gBAChB,IAAI,UAAU,GAAG,KAAK,CAAC;gBAEvB,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,EAAE;oBAC9B,IAAI,CAAC,UAAU,EAAE,CAAC;wBAChB,KAAK,CAAC,IAAI,EAAE,CAAC;wBACb,OAAO,CAAC;4BACN,EAAE,EAAE,IAAA,eAAM,GAAE;4BACZ,UAAU,EAAE,IAAA,eAAM,GAAE;4BACpB,OAAO,EAAE,KAAK;4BACd,MAAM,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,CAAC,EAAE;4BACxC,KAAK,EAAE,mBAAmB;4BAC1B,SAAS,EAAE,IAAI,IAAI,EAAE;yBACtB,CAAC,CAAC;wBACH,UAAU,GAAG,IAAI,CAAC;oBACpB,CAAC;gBACH,CAAC,EAAE,MAAM,CAAC,OAAO,IAAI,KAAK,CAAC,CAAC;gBAE5B,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;oBAChC,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC5B,CAAC,CAAC,CAAC;gBAEH,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;oBAChC,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC5B,CAAC,CAAC,CAAC;gBAEH,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE;oBACzB,IAAI,CAAC,UAAU,EAAE,CAAC;wBAChB,YAAY,CAAC,OAAO,CAAC,CAAC;wBACtB,OAAO,CAAC;4BACN,EAAE,EAAE,IAAA,eAAM,GAAE;4BACZ,UAAU,EAAE,IAAA,eAAM,GAAE;4BACpB,OAAO,EAAE,IAAI,KAAK,CAAC;4BACnB,MAAM,EAAE;gCACN,OAAO,EAAE,MAAM,CAAC,OAAO;gCACvB,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE;gCACrB,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE;gCACrB,QAAQ,EAAE,IAAI,IAAI,CAAC;gCACnB,gBAAgB,EAAE,UAAU;6BAC7B;4BACD,SAAS,EAAE,IAAI,IAAI,EAAE;4BACrB,QAAQ,EAAE;gCACR,SAAS,EAAE,aAAa;gCACxB,OAAO,EAAE,MAAM,CAAC,OAAO;6BACxB;yBACF,CAAC,CAAC;wBACH,UAAU,GAAG,IAAI,CAAC;oBACpB,CAAC;gBACH,CAAC,CAAC,CAAC;gBAEH,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;oBAC1B,IAAI,CAAC,UAAU,EAAE,CAAC;wBAChB,YAAY,CAAC,OAAO,CAAC,CAAC;wBACtB,OAAO,CAAC;4BACN,EAAE,EAAE,IAAA,eAAM,GAAE;4BACZ,UAAU,EAAE,IAAA,eAAM,GAAE;4BACpB,OAAO,EAAE,KAAK;4BACd,MAAM,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,CAAC,EAAE;4BACxC,KAAK,EAAE,kBAAkB,KAAK,CAAC,OAAO,EAAE;4BACxC,SAAS,EAAE,IAAI,IAAI,EAAE;yBACtB,CAAC,CAAC;wBACH,UAAU,GAAG,IAAI,CAAC;oBACpB,CAAC;gBACH,CAAC,CAAC,CAAC;gBAEH,0BAA0B;gBAC1B,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC9C,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;wBACrC,UAAU,CAAC,GAAG,EAAE;4BACd,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;4BACjC,IAAI,KAAK,KAAK,MAAM,CAAC,MAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gCACxC,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC;4BACrB,CAAC;wBACH,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,CAAC;oBACnB,CAAC,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACN,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC;gBACrB,CAAC;YAEH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC;oBACN,EAAE,EAAE,IAAA,eAAM,GAAE;oBACZ,UAAU,EAAE,IAAA,eAAM,GAAE;oBACpB,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,IAAI;oBACZ,KAAK,EAAE,wCAAyC,KAAe,CAAC,OAAO,EAAE;oBACzE,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;CACF,CAAC;AAiJA,wDAAsB;AA/IxB,8BAA8B;AAC9B,MAAM,mBAAmB,GAAG,OAAC,CAAC,MAAM,CAAC;IACnC,QAAQ,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,qCAAqC,CAAC;CAChG,CAAC,CAAC;AAEH,MAAM,iBAAiB,GAAS;IAC9B,IAAI,EAAE,iBAAiB;IACvB,WAAW,EAAE,oEAAoE;IACjF,MAAM,EAAE,mBAAmB;IAC3B,QAAQ,EAAE,gBAAgB;IAC1B,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,OAAqB;QACzC,IAAI,CAAC;YACH,MAAM,SAAS,GAAG;gBAChB,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,YAAY,EAAE,OAAO,CAAC,IAAI;gBAC1B,WAAW,EAAE,OAAO,CAAC,OAAO;gBAC5B,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;gBAC1C,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,aAAa;aACnD,CAAC;YAEF,IAAI,YAAY,GAA2B,EAAE,CAAC;YAE9C,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;gBACpB,IAAI,CAAC;oBACH,oCAAoC;oBACpC,MAAM,QAAQ,GAAG;wBACf,EAAE,EAAE,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU;wBACrD,MAAM,EAAE,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,6CAA6C,CAAC,CAAC,CAAC,SAAS;wBAChG,IAAI,EAAE,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,6CAA6C,CAAC,CAAC,CAAC,OAAO;wBAC5F,GAAG,EAAE,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,OAAO;qBAClE,CAAC;oBAEF,KAAK,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;wBACtD,IAAI,CAAC;4BACH,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,SAAS,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;4BAC/D,YAAY,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;wBACpC,CAAC;wBAAC,MAAM,CAAC;4BACP,YAAY,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC;wBACtC,CAAC;oBACH,CAAC;gBACH,CAAC;gBAAC,MAAM,CAAC;oBACP,2CAA2C;gBAC7C,CAAC;YACH,CAAC;YAED,OAAO;gBACL,EAAE,EAAE,IAAA,eAAM,GAAE;gBACZ,UAAU,EAAE,IAAA,eAAM,GAAE;gBACpB,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE;oBACN,GAAG,SAAS;oBACZ,GAAG,CAAC,MAAM,CAAC,QAAQ,IAAI,EAAE,QAAQ,EAAE,YAAY,EAAE,CAAC;iBACnD;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,QAAQ,EAAE;oBACR,SAAS,EAAE,aAAa;oBACxB,QAAQ,EAAE,MAAM,CAAC,QAAQ;iBAC1B;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,EAAE,EAAE,IAAA,eAAM,GAAE;gBACZ,UAAU,EAAE,IAAA,eAAM,GAAE;gBACpB,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,IAAI;gBACZ,KAAK,EAAE,8BAA+B,KAAe,CAAC,OAAO,EAAE;gBAC/D,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAC;AA0EA,8CAAiB;AAxEnB,qBAAqB;AACrB,MAAM,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;IAClC,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,uBAAuB,CAAC;IACpE,GAAG,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,qBAAqB,CAAC;IAC1D,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,yBAAyB,CAAC;CAChE,CAAC,CAAC;AAEH,MAAM,gBAAgB,GAAS;IAC7B,IAAI,EAAE,eAAe;IACrB,WAAW,EAAE,sDAAsD;IACnE,MAAM,EAAE,kBAAkB;IAC1B,QAAQ,EAAE,gBAAgB;IAC1B,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,OAAqB;QACzC,IAAI,CAAC;YACH,IAAI,OAAO,GAAG,EAAE,CAAC;YACjB,IAAI,YAAY,GAAG,EAAE,CAAC;YAEtB,IAAI,MAAM,CAAC,GAAG,EAAE,CAAC;gBACf,OAAO,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO;oBACpC,CAAC,CAAC,wBAAwB,MAAM,CAAC,GAAG,GAAG;oBACvC,CAAC,CAAC,SAAS,MAAM,CAAC,GAAG,EAAE,CAAC;gBAC1B,YAAY,GAAG,OAAO,MAAM,CAAC,GAAG,EAAE,CAAC;YACrC,CAAC;iBAAM,IAAI,MAAM,CAAC,WAAW,EAAE,CAAC;gBAC9B,OAAO,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO;oBACpC,CAAC,CAAC,8BAA8B,MAAM,CAAC,WAAW,GAAG;oBACrD,CAAC,CAAC,YAAY,MAAM,CAAC,WAAW,EAAE,CAAC;gBACrC,YAAY,GAAG,WAAW,MAAM,CAAC,WAAW,EAAE,CAAC;YACjD,CAAC;iBAAM,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;gBACvB,OAAO,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO;oBACpC,CAAC,CAAC,0BAA0B,MAAM,CAAC,IAAI,EAAE;oBACzC,CAAC,CAAC,YAAY,MAAM,CAAC,IAAI,EAAE,CAAC;gBAC9B,YAAY,GAAG,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;YACvC,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;YACnE,CAAC;YAED,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,SAAS,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;YACxE,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAC;YAEvF,OAAO;gBACL,EAAE,EAAE,IAAA,eAAM,GAAE;gBACZ,UAAU,EAAE,IAAA,eAAM,GAAE;gBACpB,OAAO,EAAE,IAAI;gBACb,MAAM,EAAE;oBACN,MAAM,EAAE,YAAY;oBACpB,SAAS;oBACT,OAAO,EAAE,MAAM,CAAC,IAAI,EAAE;oBACtB,OAAO;iBACR;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,QAAQ,EAAE;oBACR,SAAS,EAAE,eAAe;oBAC1B,MAAM,EAAE,YAAY;iBACrB;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,EAAE,EAAE,IAAA,eAAM,GAAE;gBACZ,UAAU,EAAE,IAAA,eAAM,GAAE;gBACpB,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,IAAI;gBACZ,KAAK,EAAE,4BAA6B,KAAe,CAAC,OAAO,EAAE;gBAC7D,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAC;AAOA,4CAAgB;AAGlB,kBAAe;IACb,kBAAkB;IAClB,sBAAsB;IACtB,iBAAiB;IACjB,gBAAgB;CACjB,CAAC"}