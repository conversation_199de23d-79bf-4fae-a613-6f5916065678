import { AgentEngine } from '../core/AgentEngine';
export declare class TerminalUI {
    private agent;
    private rl;
    private spinner;
    private isProcessing;
    constructor(agent: AgentEngine);
    private setupReadline;
    private setupEventHandlers;
    start(): Promise<void>;
    private showWelcome;
    private showHelp;
    private showStatus;
    private handleConfigCommand;
    private processUserInput;
    private displayToolOutput;
    private displayChange;
    private showRecentChanges;
    private showDiffCommand;
    private shutdown;
}
//# sourceMappingURL=TerminalUI.d.ts.map