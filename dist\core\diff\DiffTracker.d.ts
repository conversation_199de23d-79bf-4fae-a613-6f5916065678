import { EventEmitter } from 'events';
import { ChangeRecord, DiffResult } from '../../types';
export declare class DiffTracker extends EventEmitter {
    private changes;
    /**
     * Track changes to files
     */
    trackChanges(changes: ChangeRecord[]): void;
    /**
     * Track a single change
     */
    trackChange(change: ChangeRecord): void;
    /**
     * Generate diff between old and new content
     */
    generateDiff(oldContent: string, newContent: string, filePath: string): DiffResult;
    /**
     * Calculate diff statistics
     */
    private calculateDiffStats;
    /**
     * Get all tracked changes
     */
    getAllChanges(): ChangeRecord[];
    /**
     * Get changes for a specific file
     */
    getChangesForFile(filePath: string): ChangeRecord[];
    /**
     * Get recent changes
     */
    getRecentChanges(limit?: number): ChangeRecord[];
    /**
     * Clear all tracked changes
     */
    clearChanges(): void;
    /**
     * Remove specific change
     */
    removeChange(changeId: string): boolean;
    /**
     * Get change by ID
     */
    getChange(changeId: string): ChangeRecord | undefined;
    /**
     * Format diff for terminal display
     */
    formatDiffForTerminal(diff: DiffResult, maxLines?: number): string;
    /**
     * Get diff summary
     */
    getDiffSummary(diff: DiffResult): string;
    /**
     * Compare two files and generate diff
     */
    compareFiles(oldContent: string, newContent: string, filePath: string): Promise<DiffResult>;
    /**
     * Get changes by type
     */
    getChangesByType(type: ChangeRecord['type']): ChangeRecord[];
    /**
     * Get changes in time range
     */
    getChangesInTimeRange(startTime: Date, endTime: Date): ChangeRecord[];
    /**
     * Export changes to JSON
     */
    exportChanges(): string;
    /**
     * Import changes from JSON
     */
    importChanges(changesJson: string): void;
    /**
     * Get statistics about tracked changes
     */
    getChangeStats(): Record<string, any>;
}
//# sourceMappingURL=DiffTracker.d.ts.map