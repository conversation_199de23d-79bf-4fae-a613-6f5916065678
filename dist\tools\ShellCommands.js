"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkProcessTool = exports.getSystemInfoTool = exports.interactiveCommandTool = exports.executeCommandTool = void 0;
const child_process_1 = require("child_process");
const util_1 = require("util");
const zod_1 = require("zod");
const nanoid_1 = require("nanoid");
const execAsync = (0, util_1.promisify)(child_process_1.exec);
// Execute Shell Command Tool
const executeCommandSchema = zod_1.z.object({
    command: zod_1.z.string().describe('Shell command to execute'),
    args: zod_1.z.array(zod_1.z.string()).optional().describe('Command arguments'),
    cwd: zod_1.z.string().optional().describe('Working directory for command execution'),
    timeout: zod_1.z.number().default(30000).optional().describe('Timeout in milliseconds'),
    env: zod_1.z.record(zod_1.z.string()).optional().describe('Environment variables'),
    shell: zod_1.z.boolean().default(true).optional().describe('Execute in shell')
});
exports.executeCommandTool = {
    name: 'execute_command',
    description: 'Execute a shell command and return the output',
    schema: executeCommandSchema,
    category: 'shell-commands',
    async execute(params, context) {
        try {
            const workingDir = params.cwd
                ? (params.cwd.startsWith('/') ? params.cwd : `${context.workingDirectory}/${params.cwd}`)
                : context.workingDirectory;
            const fullCommand = params.args
                ? `${params.command} ${params.args.join(' ')}`
                : params.command;
            const options = {
                cwd: workingDir,
                timeout: params.timeout || 30000,
                env: { ...process.env, ...params.env },
                shell: params.shell !== false
            };
            const { stdout, stderr } = await execAsync(fullCommand, options);
            return {
                id: (0, nanoid_1.nanoid)(),
                toolCallId: (0, nanoid_1.nanoid)(),
                success: true,
                output: {
                    command: fullCommand,
                    stdout: stdout.trim(),
                    stderr: stderr.trim(),
                    exitCode: 0,
                    workingDirectory: workingDir
                },
                timestamp: new Date(),
                metadata: {
                    operation: 'execute',
                    command: fullCommand,
                    executionTime: Date.now()
                }
            };
        }
        catch (error) {
            return {
                id: (0, nanoid_1.nanoid)(),
                toolCallId: (0, nanoid_1.nanoid)(),
                success: false,
                output: {
                    command: params.command,
                    stdout: error.stdout || '',
                    stderr: error.stderr || error.message,
                    exitCode: error.code || 1,
                    workingDirectory: context.workingDirectory
                },
                error: `Command failed: ${error.message}`,
                timestamp: new Date()
            };
        }
    }
};
// Interactive Shell Command Tool
const interactiveCommandSchema = zod_1.z.object({
    command: zod_1.z.string().describe('Command to start interactive session'),
    inputs: zod_1.z.array(zod_1.z.string()).optional().describe('Inputs to send to the interactive session'),
    timeout: zod_1.z.number().default(60000).optional().describe('Timeout in milliseconds'),
    cwd: zod_1.z.string().optional().describe('Working directory')
});
exports.interactiveCommandTool = {
    name: 'interactive_command',
    description: 'Execute an interactive command with input/output handling',
    schema: interactiveCommandSchema,
    category: 'shell-commands',
    async execute(params, context) {
        return new Promise((resolve) => {
            try {
                const workingDir = params.cwd
                    ? `${context.workingDirectory}/${params.cwd}`
                    : context.workingDirectory;
                const child = (0, child_process_1.spawn)(params.command, [], {
                    cwd: workingDir,
                    stdio: ['pipe', 'pipe', 'pipe'],
                    shell: true
                });
                let stdout = '';
                let stderr = '';
                let isResolved = false;
                const timeout = setTimeout(() => {
                    if (!isResolved) {
                        child.kill();
                        resolve({
                            id: (0, nanoid_1.nanoid)(),
                            toolCallId: (0, nanoid_1.nanoid)(),
                            success: false,
                            output: { stdout, stderr, exitCode: -1 },
                            error: 'Command timed out',
                            timestamp: new Date()
                        });
                        isResolved = true;
                    }
                }, params.timeout || 60000);
                child.stdout?.on('data', (data) => {
                    stdout += data.toString();
                });
                child.stderr?.on('data', (data) => {
                    stderr += data.toString();
                });
                child.on('close', (code) => {
                    if (!isResolved) {
                        clearTimeout(timeout);
                        resolve({
                            id: (0, nanoid_1.nanoid)(),
                            toolCallId: (0, nanoid_1.nanoid)(),
                            success: code === 0,
                            output: {
                                command: params.command,
                                stdout: stdout.trim(),
                                stderr: stderr.trim(),
                                exitCode: code || 0,
                                workingDirectory: workingDir
                            },
                            timestamp: new Date(),
                            metadata: {
                                operation: 'interactive',
                                command: params.command
                            }
                        });
                        isResolved = true;
                    }
                });
                child.on('error', (error) => {
                    if (!isResolved) {
                        clearTimeout(timeout);
                        resolve({
                            id: (0, nanoid_1.nanoid)(),
                            toolCallId: (0, nanoid_1.nanoid)(),
                            success: false,
                            output: { stdout, stderr, exitCode: -1 },
                            error: `Process error: ${error.message}`,
                            timestamp: new Date()
                        });
                        isResolved = true;
                    }
                });
                // Send inputs if provided
                if (params.inputs && params.inputs.length > 0) {
                    params.inputs.forEach((input, index) => {
                        setTimeout(() => {
                            child.stdin?.write(input + '\n');
                            if (index === params.inputs.length - 1) {
                                child.stdin?.end();
                            }
                        }, index * 1000);
                    });
                }
                else {
                    child.stdin?.end();
                }
            }
            catch (error) {
                resolve({
                    id: (0, nanoid_1.nanoid)(),
                    toolCallId: (0, nanoid_1.nanoid)(),
                    success: false,
                    output: null,
                    error: `Failed to start interactive command: ${error.message}`,
                    timestamp: new Date()
                });
            }
        });
    }
};
// Get System Information Tool
const getSystemInfoSchema = zod_1.z.object({
    detailed: zod_1.z.boolean().default(false).optional().describe('Include detailed system information')
});
exports.getSystemInfoTool = {
    name: 'get_system_info',
    description: 'Get system information including OS, architecture, and environment',
    schema: getSystemInfoSchema,
    category: 'shell-commands',
    async execute(params, context) {
        try {
            const basicInfo = {
                platform: process.platform,
                architecture: process.arch,
                nodeVersion: process.version,
                workingDirectory: context.workingDirectory,
                environment: process.env.NODE_ENV || 'development'
            };
            let detailedInfo = {};
            if (params.detailed) {
                try {
                    // Get additional system information
                    const commands = {
                        os: process.platform === 'win32' ? 'ver' : 'uname -a',
                        memory: process.platform === 'win32' ? 'wmic computersystem get TotalPhysicalMemory' : 'free -h',
                        disk: process.platform === 'win32' ? 'wmic logicaldisk get size,freespace,caption' : 'df -h',
                        cpu: process.platform === 'win32' ? 'wmic cpu get name' : 'lscpu'
                    };
                    for (const [key, command] of Object.entries(commands)) {
                        try {
                            const { stdout } = await execAsync(command, { timeout: 5000 });
                            detailedInfo[key] = stdout.trim();
                        }
                        catch {
                            detailedInfo[key] = 'Not available';
                        }
                    }
                }
                catch {
                    // Ignore errors in detailed info gathering
                }
            }
            return {
                id: (0, nanoid_1.nanoid)(),
                toolCallId: (0, nanoid_1.nanoid)(),
                success: true,
                output: {
                    ...basicInfo,
                    ...(params.detailed && { detailed: detailedInfo })
                },
                timestamp: new Date(),
                metadata: {
                    operation: 'system-info',
                    detailed: params.detailed
                }
            };
        }
        catch (error) {
            return {
                id: (0, nanoid_1.nanoid)(),
                toolCallId: (0, nanoid_1.nanoid)(),
                success: false,
                output: null,
                error: `Failed to get system info: ${error.message}`,
                timestamp: new Date()
            };
        }
    }
};
// Check Process Tool
const checkProcessSchema = zod_1.z.object({
    processName: zod_1.z.string().optional().describe('Process name to check'),
    pid: zod_1.z.number().optional().describe('Process ID to check'),
    port: zod_1.z.number().optional().describe('Port to check if in use')
});
exports.checkProcessTool = {
    name: 'check_process',
    description: 'Check if a process is running or if a port is in use',
    schema: checkProcessSchema,
    category: 'shell-commands',
    async execute(params, context) {
        try {
            let command = '';
            let searchTarget = '';
            if (params.pid) {
                command = process.platform === 'win32'
                    ? `tasklist /FI "PID eq ${params.pid}"`
                    : `ps -p ${params.pid}`;
                searchTarget = `PID ${params.pid}`;
            }
            else if (params.processName) {
                command = process.platform === 'win32'
                    ? `tasklist /FI "IMAGENAME eq ${params.processName}"`
                    : `pgrep -f ${params.processName}`;
                searchTarget = `process ${params.processName}`;
            }
            else if (params.port) {
                command = process.platform === 'win32'
                    ? `netstat -an | findstr :${params.port}`
                    : `lsof -i :${params.port}`;
                searchTarget = `port ${params.port}`;
            }
            else {
                throw new Error('Must specify either processName, pid, or port');
            }
            const { stdout, stderr } = await execAsync(command, { timeout: 10000 });
            const isRunning = stdout.trim().length > 0 && !stdout.includes('No tasks are running');
            return {
                id: (0, nanoid_1.nanoid)(),
                toolCallId: (0, nanoid_1.nanoid)(),
                success: true,
                output: {
                    target: searchTarget,
                    isRunning,
                    details: stdout.trim(),
                    command
                },
                timestamp: new Date(),
                metadata: {
                    operation: 'check-process',
                    target: searchTarget
                }
            };
        }
        catch (error) {
            return {
                id: (0, nanoid_1.nanoid)(),
                toolCallId: (0, nanoid_1.nanoid)(),
                success: false,
                output: null,
                error: `Failed to check process: ${error.message}`,
                timestamp: new Date()
            };
        }
    }
};
exports.default = [
    exports.executeCommandTool,
    exports.interactiveCommandTool,
    exports.getSystemInfoTool,
    exports.checkProcessTool
];
//# sourceMappingURL=ShellCommands.js.map