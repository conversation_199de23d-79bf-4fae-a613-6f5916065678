{"version": 3, "file": "AgentEngine.js", "sourceRoot": "", "sources": ["../../src/core/AgentEngine.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,mCAAsC;AACtC,mCAAgC;AAChC,oCAQkB;AAClB,gDAA6C;AAC7C,uDAAoD;AACpD,6DAA0D;AAC1D,oDAAiD;AACjD,0EAAuE;AACvE,iDAA8C;AAE9C;;;GAGG;AACH,MAAa,WAAY,SAAQ,qBAAY;IACnC,MAAM,CAAc;IACpB,UAAU,CAAa;IACvB,YAAY,CAAe;IAC3B,cAAc,CAAiB;IAC/B,WAAW,CAAc;IACzB,cAAc,CAAuB;IACrC,UAAU,CAAa;IACvB,OAAO,CAAe;IACtB,YAAY,GAAY,KAAK,CAAC;IAEtC,YAAY,MAAmB,EAAE,gBAAwB;QACvD,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YACxD,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,kBAAU,CAC/B,+BAA+B,KAAK,CAAC,OAAO,EAAE,EAC9C,uBAAuB,EACvB,QAAQ,EACR,KAAK,EACL,EAAE,aAAa,EAAE,KAAK,EAAE,CACzB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,gBAAwB;QACzD,6BAA6B;QAC7B,IAAI,CAAC,UAAU,GAAG,IAAI,uBAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC9C,IAAI,CAAC,YAAY,GAAG,IAAI,2BAAY,EAAE,CAAC;QACvC,IAAI,CAAC,cAAc,GAAG,IAAI,+BAAc,CAAC,gBAAgB,CAAC,CAAC;QAC3D,IAAI,CAAC,WAAW,GAAG,IAAI,yBAAW,EAAE,CAAC;QACrC,IAAI,CAAC,cAAc,GAAG,IAAI,2CAAoB,EAAE,CAAC;QACjD,IAAI,CAAC,UAAU,GAAG,IAAI,uBAAU,CAAC,gBAAgB,CAAC,CAAC;QAEnD,qBAAqB;QACrB,IAAI,CAAC,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,CAAC;QAE7D,0BAA0B;QAC1B,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE1B,sBAAsB;QACtB,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAE/B,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;IACtD,CAAC;IAEO,kBAAkB;QACxB,wBAAwB;QACxB,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,eAAe,EAAE,CAAC,MAAkB,EAAE,EAAE;YAC3D,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;QAEH,uBAAuB;QACvB,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,iBAAiB,EAAE,CAAC,MAAM,EAAE,EAAE;YAChD,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACxC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,aAAa;QACb,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,eAAe,EAAE,CAAC,SAAS,EAAE,EAAE;YAChD,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,iBAAiB;QACjB,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAiB,EAAE,EAAE;YACrC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC7B,4CAA4C;QAC5C,MAAM,aAAa,GAAG,wDAAa,yBAAyB,GAAC,CAAC;QAC9D,MAAM,YAAY,GAAG,aAAa,CAAC,OAAO,CAAC;QAC3C,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE,CAAC;YAChC,MAAM,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC;QAED,0CAA0C;QAC1C,MAAM,WAAW,GAAG,wDAAa,wBAAwB,GAAC,CAAC;QAC3D,MAAM,UAAU,GAAG,WAAW,CAAC,OAAO,CAAC;QACvC,KAAK,MAAM,IAAI,IAAI,UAAU,EAAE,CAAC;YAC9B,MAAM,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC;QAED,0CAA0C;QAC1C,MAAM,SAAS,GAAG,wDAAa,wBAAwB,GAAC,CAAC;QACzD,MAAM,QAAQ,GAAG,SAAS,CAAC,OAAO,CAAC;QACnC,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE,CAAC;YAC5B,MAAM,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC;QAED,4CAA4C;QAC5C,MAAM,cAAc,GAAG,wDAAa,yBAAyB,GAAC,CAAC;QAC/D,MAAM,aAAa,GAAG,cAAc,CAAC,OAAO,CAAC;QAC7C,KAAK,MAAM,IAAI,IAAI,aAAa,EAAE,CAAC;YACjC,MAAM,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC;QAED,+CAA+C;QAC/C,MAAM,aAAa,GAAG,wDAAa,4BAA4B,GAAC,CAAC;QACjE,MAAM,YAAY,GAAG,aAAa,CAAC,OAAO,CAAC;QAC3C,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE,CAAC;YAChC,MAAM,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;QAC7C,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,KAAa;QAC9B,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,MAAM,IAAI,kBAAU,CAAC,uCAAuC,EAAE,YAAY,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;QACnG,CAAC;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAEzB,IAAI,CAAC;YACH,sBAAsB;YACtB,MAAM,WAAW,GAAwB;gBACvC,EAAE,EAAE,IAAA,eAAM,GAAE;gBACZ,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE,KAAK;gBACd,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;YAEF,8BAA8B;YAC9B,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAEnD,8BAA8B;YAC9B,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;YAElE,kCAAkC;YAClC,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAEvC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,WAAW,CAAC,KAAmB,CAAC,CAAC;QACxC,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;YAC1B,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACpC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,uBAAuB;QACnC,IAAI,aAAa,GAAG,EAAE,CAAC,CAAC,yBAAyB;QACjD,IAAI,SAAS,GAAG,CAAC,CAAC;QAElB,OAAO,SAAS,GAAG,aAAa,EAAE,CAAC;YACjC,SAAS,EAAE,CAAC;YAEZ,IAAI,CAAC;gBACH,kCAAkC;gBAClC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,CAChD,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAChC,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,EAClC,IAAI,CAAC,OAAO,CACb,CAAC;gBAEF,kCAAkC;gBAClC,MAAM,gBAAgB,GAAwB;oBAC5C,EAAE,EAAE,IAAA,eAAM,GAAE;oBACZ,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,QAAQ,CAAC,OAAO;oBACzB,SAAS,EAAE,IAAI,IAAI,EAAE;oBACrB,SAAS,EAAE,QAAQ,CAAC,SAAS;iBAC9B,CAAC;gBAEF,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAExD,gCAAgC;gBAChC,IAAI,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACxD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;oBACpE,gBAAgB,CAAC,WAAW,GAAG,WAAW,CAAC;oBAE3C,sCAAsC;oBACtC,MAAM,iBAAiB,GAAG,IAAI,CAAC,uBAAuB,CAAC,WAAW,CAAC,CAAC;oBACpE,IAAI,CAAC,iBAAiB,EAAE,CAAC;wBACvB,MAAM;oBACR,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,kCAAkC;oBAClC,MAAM;gBACR,CAAC;gBAED,sCAAsC;gBACtC,MAAM,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAExD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,WAAW,CAAC,KAAmB,CAAC,CAAC;gBACtC,MAAM;YACR,CAAC;QACH,CAAC;QAED,IAAI,SAAS,IAAI,aAAa,EAAE,CAAC;YAC/B,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,EAAE,UAAU,EAAE,aAAa,EAAE,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAAC,SAAqB;QAClD,MAAM,OAAO,GAAiB,EAAE,CAAC;QAEjC,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,IAAI,CAAC;gBACH,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,QAAQ,CAAC,CAAC;gBAE9C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,CAChD,QAAQ,CAAC,IAAI,EACb,QAAQ,CAAC,UAAU,EACnB,IAAI,CAAC,OAAO,CACb,CAAC;gBAEF,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACrB,IAAI,CAAC,IAAI,CAAC,0BAA0B,EAAE,MAAM,CAAC,CAAC;YAEhD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,WAAW,GAAe;oBAC9B,EAAE,EAAE,IAAA,eAAM,GAAE;oBACZ,UAAU,EAAE,QAAQ,CAAC,EAAE;oBACvB,OAAO,EAAE,KAAK;oBACd,MAAM,EAAE,IAAI;oBACZ,KAAK,EAAG,KAAe,CAAC,OAAO;oBAC/B,SAAS,EAAE,IAAI,IAAI,EAAE;iBACtB,CAAC;gBAEF,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAC1B,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,WAAW,CAAC,CAAC;YAClD,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,uBAAuB,CAAC,WAAyB;QACvD,qDAAqD;QACrD,OAAO,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAC/B,MAAM,CAAC,QAAQ,EAAE,iBAAiB,KAAK,IAAI;YAC3C,MAAM,CAAC,QAAQ,EAAE,gBAAgB,KAAK,IAAI,CAC3C,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,MAAkB;QACzC,sCAAsC;QACtC,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,QAAQ,EAAE,aAAa,EAAE,CAAC;YACrD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;QAC7D,CAAC;QAED,4CAA4C;QAC5C,IAAI,MAAM,CAAC,QAAQ,EAAE,WAAW,EAAE,CAAC;YACjC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,MAAM,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,KAAiB;QACnC,OAAO,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;QAErC,oDAAoD;QACpD,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC;YACtB,IAAI,CAAC,IAAI,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YAC7C,oCAAoC;QACtC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;QAClC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,UAAU;QACR,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,SAA+B;QAChD,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,EAAE,CAAC;QAC/C,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC1C,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK;QACT,IAAI,CAAC,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,iBAAiB,EAAE,CAAC;QAC7D,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ;QACZ,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACpD,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;IAClC,CAAC;CACF;AAzTD,kCAyTC"}