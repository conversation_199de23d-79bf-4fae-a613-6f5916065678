"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.gitLogTool = exports.gitDiffTool = exports.gitBranchTool = exports.gitCommitTool = exports.gitAddTool = exports.gitStatusTool = void 0;
const zod_1 = require("zod");
const nanoid_1 = require("nanoid");
const GitManager_1 = require("../core/git/GitManager");
// Git Status Tool
const gitStatusSchema = zod_1.z.object({
    detailed: zod_1.z.boolean().default(false).optional().describe('Include detailed status information')
});
exports.gitStatusTool = {
    name: 'git_status',
    description: 'Get the current git repository status',
    schema: gitStatusSchema,
    category: 'git-operations',
    async execute(params, context) {
        try {
            const gitManager = new GitManager_1.GitManager(context.workingDirectory);
            if (!await gitManager.isRepository()) {
                return {
                    id: (0, nanoid_1.nanoid)(),
                    toolCallId: (0, nanoid_1.nanoid)(),
                    success: false,
                    output: null,
                    error: 'Not a git repository',
                    timestamp: new Date()
                };
            }
            const status = await gitManager.getStatus();
            const repoInfo = params.detailed ? await gitManager.getRepositoryInfo() : null;
            return {
                id: (0, nanoid_1.nanoid)(),
                toolCallId: (0, nanoid_1.nanoid)(),
                success: true,
                output: {
                    status,
                    ...(params.detailed && { repository: repoInfo })
                },
                timestamp: new Date(),
                metadata: {
                    operation: 'status',
                    hasChanges: status.modified.length > 0 || status.added.length > 0 || status.deleted.length > 0
                }
            };
        }
        catch (error) {
            return {
                id: (0, nanoid_1.nanoid)(),
                toolCallId: (0, nanoid_1.nanoid)(),
                success: false,
                output: null,
                error: `Git status failed: ${error.message}`,
                timestamp: new Date()
            };
        }
    }
};
// Git Add Tool
const gitAddSchema = zod_1.z.object({
    files: zod_1.z.array(zod_1.z.string()).optional().describe('Files to stage (if empty, stages all changes)'),
    all: zod_1.z.boolean().default(false).optional().describe('Stage all changes including deletions')
});
exports.gitAddTool = {
    name: 'git_add',
    description: 'Stage files for commit',
    schema: gitAddSchema,
    category: 'git-operations',
    async execute(params, context) {
        try {
            const gitManager = new GitManager_1.GitManager(context.workingDirectory);
            if (params.all || !params.files || params.files.length === 0) {
                await gitManager.stageAll();
            }
            else {
                await gitManager.stageFiles(params.files);
            }
            const changeRecord = {
                id: (0, nanoid_1.nanoid)(),
                type: 'git-operation',
                path: 'git-staging',
                timestamp: new Date(),
                metadata: {
                    operation: 'stage',
                    files: params.files || ['all']
                }
            };
            return {
                id: (0, nanoid_1.nanoid)(),
                toolCallId: (0, nanoid_1.nanoid)(),
                success: true,
                output: {
                    staged: params.files || 'all changes',
                    operation: 'stage'
                },
                timestamp: new Date(),
                metadata: {
                    operation: 'stage',
                    fileChanges: [changeRecord]
                }
            };
        }
        catch (error) {
            return {
                id: (0, nanoid_1.nanoid)(),
                toolCallId: (0, nanoid_1.nanoid)(),
                success: false,
                output: null,
                error: `Git add failed: ${error.message}`,
                timestamp: new Date()
            };
        }
    }
};
// Git Commit Tool
const gitCommitSchema = zod_1.z.object({
    message: zod_1.z.string().describe('Commit message'),
    addAll: zod_1.z.boolean().default(false).optional().describe('Stage all changes before committing')
});
exports.gitCommitTool = {
    name: 'git_commit',
    description: 'Commit staged changes',
    schema: gitCommitSchema,
    category: 'git-operations',
    async execute(params, context) {
        try {
            const gitManager = new GitManager_1.GitManager(context.workingDirectory);
            // Stage all changes if requested
            if (params.addAll) {
                await gitManager.stageAll();
            }
            const commit = await gitManager.commit(params.message);
            const changeRecord = {
                id: (0, nanoid_1.nanoid)(),
                type: 'git-operation',
                path: 'git-commit',
                timestamp: new Date(),
                metadata: {
                    operation: 'commit',
                    commit: commit
                }
            };
            return {
                id: (0, nanoid_1.nanoid)(),
                toolCallId: (0, nanoid_1.nanoid)(),
                success: true,
                output: {
                    commit,
                    message: params.message
                },
                timestamp: new Date(),
                metadata: {
                    operation: 'commit',
                    fileChanges: [changeRecord]
                }
            };
        }
        catch (error) {
            return {
                id: (0, nanoid_1.nanoid)(),
                toolCallId: (0, nanoid_1.nanoid)(),
                success: false,
                output: null,
                error: `Git commit failed: ${error.message}`,
                timestamp: new Date()
            };
        }
    }
};
// Git Branch Tool
const gitBranchSchema = zod_1.z.object({
    action: zod_1.z.enum(['list', 'create', 'switch', 'delete']).describe('Branch action to perform'),
    branchName: zod_1.z.string().optional().describe('Branch name (required for create, switch, delete)'),
    switchTo: zod_1.z.boolean().default(true).optional().describe('Switch to branch after creating')
});
exports.gitBranchTool = {
    name: 'git_branch',
    description: 'Manage git branches',
    schema: gitBranchSchema,
    category: 'git-operations',
    async execute(params, context) {
        try {
            const gitManager = new GitManager_1.GitManager(context.workingDirectory);
            let result = {};
            switch (params.action) {
                case 'list':
                    const branches = await gitManager.getBranches();
                    result = { branches, current: context.gitRepository?.currentBranch };
                    break;
                case 'create':
                    if (!params.branchName) {
                        throw new Error('Branch name is required for create action');
                    }
                    await gitManager.createBranch(params.branchName, params.switchTo);
                    result = { created: params.branchName, switchedTo: params.switchTo };
                    break;
                case 'switch':
                    if (!params.branchName) {
                        throw new Error('Branch name is required for switch action');
                    }
                    await gitManager.switchBranch(params.branchName);
                    result = { switchedTo: params.branchName };
                    break;
                case 'delete':
                    // Note: Delete operation would need additional implementation in GitManager
                    throw new Error('Delete branch operation not yet implemented');
                default:
                    throw new Error(`Unknown branch action: ${params.action}`);
            }
            return {
                id: (0, nanoid_1.nanoid)(),
                toolCallId: (0, nanoid_1.nanoid)(),
                success: true,
                output: {
                    action: params.action,
                    ...result
                },
                timestamp: new Date(),
                metadata: {
                    operation: `branch-${params.action}`,
                    branchName: params.branchName
                }
            };
        }
        catch (error) {
            return {
                id: (0, nanoid_1.nanoid)(),
                toolCallId: (0, nanoid_1.nanoid)(),
                success: false,
                output: null,
                error: `Git branch operation failed: ${error.message}`,
                timestamp: new Date()
            };
        }
    }
};
// Git Diff Tool
const gitDiffSchema = zod_1.z.object({
    files: zod_1.z.array(zod_1.z.string()).optional().describe('Specific files to diff'),
    staged: zod_1.z.boolean().default(false).optional().describe('Show diff of staged changes'),
    commit1: zod_1.z.string().optional().describe('First commit for comparison'),
    commit2: zod_1.z.string().optional().describe('Second commit for comparison')
});
exports.gitDiffTool = {
    name: 'git_diff',
    description: 'Show differences in git repository',
    schema: gitDiffSchema,
    category: 'git-operations',
    async execute(params, context) {
        try {
            const gitManager = new GitManager_1.GitManager(context.workingDirectory);
            let diff;
            if (params.commit1 && params.commit2) {
                diff = await gitManager.getDiffBetweenCommits(params.commit1, params.commit2);
            }
            else {
                diff = await gitManager.getDiff(params.files);
            }
            return {
                id: (0, nanoid_1.nanoid)(),
                toolCallId: (0, nanoid_1.nanoid)(),
                success: true,
                output: {
                    diff,
                    files: params.files,
                    staged: params.staged,
                    commits: params.commit1 && params.commit2 ? [params.commit1, params.commit2] : undefined
                },
                timestamp: new Date(),
                metadata: {
                    operation: 'diff',
                    hasChanges: diff.length > 0
                }
            };
        }
        catch (error) {
            return {
                id: (0, nanoid_1.nanoid)(),
                toolCallId: (0, nanoid_1.nanoid)(),
                success: false,
                output: null,
                error: `Git diff failed: ${error.message}`,
                timestamp: new Date()
            };
        }
    }
};
// Git Log Tool
const gitLogSchema = zod_1.z.object({
    limit: zod_1.z.number().default(10).optional().describe('Number of commits to show'),
    oneline: zod_1.z.boolean().default(false).optional().describe('Show one line per commit')
});
exports.gitLogTool = {
    name: 'git_log',
    description: 'Show git commit history',
    schema: gitLogSchema,
    category: 'git-operations',
    async execute(params, context) {
        try {
            const gitManager = new GitManager_1.GitManager(context.workingDirectory);
            const commits = await gitManager.getCommitHistory(params.limit);
            return {
                id: (0, nanoid_1.nanoid)(),
                toolCallId: (0, nanoid_1.nanoid)(),
                success: true,
                output: {
                    commits,
                    count: commits.length,
                    limit: params.limit
                },
                timestamp: new Date(),
                metadata: {
                    operation: 'log',
                    commitCount: commits.length
                }
            };
        }
        catch (error) {
            return {
                id: (0, nanoid_1.nanoid)(),
                toolCallId: (0, nanoid_1.nanoid)(),
                success: false,
                output: null,
                error: `Git log failed: ${error.message}`,
                timestamp: new Date()
            };
        }
    }
};
exports.default = [
    exports.gitStatusTool,
    exports.gitAddTool,
    exports.gitCommitTool,
    exports.gitBranchTool,
    exports.gitDiffTool,
    exports.gitLogTool
];
//# sourceMappingURL=GitOperations.js.map