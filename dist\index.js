"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DEFAULT_CONFIG = exports.VERSION = exports.TerminalUI = exports.GitManager = exports.SemanticSearchEngine = exports.DiffTracker = exports.ConfigManager = exports.ContextManager = exports.ToolRegistry = exports.AIProvider = exports.AgentEngine = void 0;
// Main exports for the Autonomous Agent CLI
var AgentEngine_1 = require("./core/AgentEngine");
Object.defineProperty(exports, "AgentEngine", { enumerable: true, get: function () { return AgentEngine_1.AgentEngine; } });
var AIProvider_1 = require("./core/ai/AIProvider");
Object.defineProperty(exports, "AIProvider", { enumerable: true, get: function () { return AIProvider_1.AIProvider; } });
var ToolRegistry_1 = require("./core/tools/ToolRegistry");
Object.defineProperty(exports, "ToolRegistry", { enumerable: true, get: function () { return ToolRegistry_1.ToolRegistry; } });
var ContextManager_1 = require("./core/context/ContextManager");
Object.defineProperty(exports, "ContextManager", { enumerable: true, get: function () { return ContextManager_1.ContextManager; } });
var ConfigManager_1 = require("./core/config/ConfigManager");
Object.defineProperty(exports, "ConfigManager", { enumerable: true, get: function () { return ConfigManager_1.ConfigManager; } });
var DiffTracker_1 = require("./core/diff/DiffTracker");
Object.defineProperty(exports, "DiffTracker", { enumerable: true, get: function () { return DiffTracker_1.DiffTracker; } });
var SemanticSearchEngine_1 = require("./core/semantic/SemanticSearchEngine");
Object.defineProperty(exports, "SemanticSearchEngine", { enumerable: true, get: function () { return SemanticSearchEngine_1.SemanticSearchEngine; } });
var GitManager_1 = require("./core/git/GitManager");
Object.defineProperty(exports, "GitManager", { enumerable: true, get: function () { return GitManager_1.GitManager; } });
var TerminalUI_1 = require("./ui/TerminalUI");
Object.defineProperty(exports, "TerminalUI", { enumerable: true, get: function () { return TerminalUI_1.TerminalUI; } });
// Export types
__exportStar(require("./types"), exports);
// Export tools
__exportStar(require("./tools/FileOperations"), exports);
__exportStar(require("./tools/ShellCommands"), exports);
__exportStar(require("./tools/GitOperations"), exports);
__exportStar(require("./tools/SemanticSearch"), exports);
__exportStar(require("./tools/ContextManagement"), exports);
// Version
exports.VERSION = '1.0.0';
// Default configuration
exports.DEFAULT_CONFIG = {
    provider: 'openai',
    model: 'gpt-4-turbo-preview',
    temperature: 0.7,
    maxTokens: 4000
};
//# sourceMappingURL=index.js.map