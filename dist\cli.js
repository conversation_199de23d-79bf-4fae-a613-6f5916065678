#!/usr/bin/env node
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const commander_1 = require("commander");
const chalk_1 = __importDefault(require("chalk"));
const ora_1 = __importDefault(require("ora"));
const inquirer_1 = __importDefault(require("inquirer"));
const dotenv_1 = require("dotenv");
const AgentEngine_1 = require("./core/AgentEngine");
const TerminalUI_1 = require("./ui/TerminalUI");
const ConfigManager_1 = require("./core/config/ConfigManager");
// Load environment variables
(0, dotenv_1.config)();
const program = new commander_1.Command();
program
    .name('agent')
    .description('Autonomous AI-powered CLI tool')
    .version('1.0.0');
// Interactive mode command
program
    .command('chat')
    .description('Start interactive chat mode')
    .option('-p, --provider <provider>', 'AI provider (openai, deepseek, ollama)', 'openai')
    .option('-m, --model <model>', 'Model to use')
    .option('-k, --api-key <key>', 'API key for the provider')
    .option('-u, --base-url <url>', 'Base URL for the provider')
    .option('-t, --temperature <temp>', 'Temperature for AI responses', '0.7')
    .option('-d, --directory <dir>', 'Working directory', process.cwd())
    .action(async (options) => {
    try {
        const configManager = new ConfigManager_1.ConfigManager();
        let agentConfig;
        // Try to load existing config or create new one
        try {
            agentConfig = await configManager.loadConfig();
            console.log(chalk_1.default.green('✓ Loaded existing configuration'));
        }
        catch {
            console.log(chalk_1.default.yellow('No existing configuration found. Setting up...'));
            agentConfig = await setupConfiguration(options);
            await configManager.saveConfig(agentConfig);
        }
        // Override with command line options
        if (options.provider)
            agentConfig.provider = options.provider;
        if (options.model)
            agentConfig.model = options.model;
        if (options.apiKey)
            agentConfig.apiKey = options.apiKey;
        if (options.baseUrl)
            agentConfig.baseUrl = options.baseUrl;
        if (options.temperature)
            agentConfig.temperature = parseFloat(options.temperature);
        // Validate configuration
        if (!agentConfig.apiKey && agentConfig.provider !== 'ollama') {
            console.error(chalk_1.default.red('Error: API key is required for this provider'));
            process.exit(1);
        }
        // Initialize agent
        const spinner = (0, ora_1.default)('Initializing agent...').start();
        const agent = new AgentEngine_1.AgentEngine(agentConfig, options.directory);
        // Wait for initialization
        await new Promise((resolve) => {
            agent.once('initialized', resolve);
        });
        spinner.succeed('Agent initialized successfully');
        // Start terminal UI
        const terminalUI = new TerminalUI_1.TerminalUI(agent);
        await terminalUI.start();
    }
    catch (error) {
        console.error(chalk_1.default.red('Error starting agent:'), error.message);
        process.exit(1);
    }
});
// One-shot command execution
program
    .command('run <prompt>')
    .description('Execute a single prompt and exit')
    .option('-p, --provider <provider>', 'AI provider (openai, deepseek, ollama)', 'openai')
    .option('-m, --model <model>', 'Model to use')
    .option('-k, --api-key <key>', 'API key for the provider')
    .option('-u, --base-url <url>', 'Base URL for the provider')
    .option('-d, --directory <dir>', 'Working directory', process.cwd())
    .action(async (prompt, options) => {
    try {
        const configManager = new ConfigManager_1.ConfigManager();
        let agentConfig;
        try {
            agentConfig = await configManager.loadConfig();
        }
        catch {
            agentConfig = await setupConfiguration(options);
        }
        // Override with command line options
        if (options.provider)
            agentConfig.provider = options.provider;
        if (options.model)
            agentConfig.model = options.model;
        if (options.apiKey)
            agentConfig.apiKey = options.apiKey;
        if (options.baseUrl)
            agentConfig.baseUrl = options.baseUrl;
        // Initialize and run
        const spinner = (0, ora_1.default)('Processing request...').start();
        const agent = new AgentEngine_1.AgentEngine(agentConfig, options.directory);
        await new Promise((resolve) => {
            agent.once('initialized', resolve);
        });
        // Set up event handlers for output
        agent.on('processing-started', () => {
            spinner.text = 'Agent is working...';
        });
        agent.on('tool-execution-started', (toolCall) => {
            spinner.text = `Executing: ${toolCall.name}`;
        });
        agent.on('processing-completed', () => {
            spinner.succeed('Task completed');
        });
        // Process the prompt
        await agent.processInput(prompt);
    }
    catch (error) {
        console.error(chalk_1.default.red('Error:'), error.message);
        process.exit(1);
    }
});
// Configuration management commands
program
    .command('config')
    .description('Manage agent configuration')
    .option('--show', 'Show current configuration')
    .option('--reset', 'Reset configuration')
    .action(async (options) => {
    const configManager = new ConfigManager_1.ConfigManager();
    if (options.show) {
        try {
            const config = await configManager.loadConfig();
            console.log(chalk_1.default.blue('Current Configuration:'));
            console.log(JSON.stringify({
                provider: config.provider,
                model: config.model,
                baseUrl: config.baseUrl,
                temperature: config.temperature,
                maxTokens: config.maxTokens
            }, null, 2));
        }
        catch {
            console.log(chalk_1.default.yellow('No configuration found'));
        }
    }
    else if (options.reset) {
        await configManager.resetConfig();
        console.log(chalk_1.default.green('Configuration reset successfully'));
    }
    else {
        const config = await setupConfiguration({});
        await configManager.saveConfig(config);
        console.log(chalk_1.default.green('Configuration saved successfully'));
    }
});
// Setup configuration interactively
async function setupConfiguration(options) {
    console.log(chalk_1.default.blue('\n🤖 Agent Configuration Setup\n'));
    const questions = [
        {
            type: 'list',
            name: 'provider',
            message: 'Select AI provider:',
            choices: [
                { name: 'OpenAI', value: 'openai' },
                { name: 'Deepseek', value: 'deepseek' },
                { name: 'Ollama (Local)', value: 'ollama' }
            ],
            default: options.provider || 'openai'
        },
        {
            type: 'input',
            name: 'apiKey',
            message: 'Enter API key:',
            when: (answers) => answers.provider !== 'ollama',
            validate: (input) => input.length > 0 || 'API key is required'
        },
        {
            type: 'input',
            name: 'baseUrl',
            message: 'Enter base URL (optional):',
            when: (answers) => answers.provider !== 'openai'
        },
        {
            type: 'input',
            name: 'model',
            message: 'Enter model name:',
            default: (answers) => {
                switch (answers.provider) {
                    case 'openai': return 'gpt-4-turbo-preview';
                    case 'deepseek': return 'deepseek-chat';
                    case 'ollama': return 'llama2';
                    default: return 'gpt-4-turbo-preview';
                }
            }
        },
        {
            type: 'number',
            name: 'temperature',
            message: 'Temperature (0.0-2.0):',
            default: 0.7,
            validate: (input) => input >= 0 && input <= 2
        },
        {
            type: 'number',
            name: 'maxTokens',
            message: 'Max tokens (optional):',
            default: 4000
        }
    ];
    const answers = await inquirer_1.default.prompt(questions);
    return {
        provider: answers.provider,
        apiKey: answers.apiKey || 'ollama',
        baseUrl: answers.baseUrl,
        model: answers.model,
        temperature: answers.temperature,
        maxTokens: answers.maxTokens
    };
}
// Error handling
process.on('uncaughtException', (error) => {
    console.error(chalk_1.default.red('Uncaught Exception:'), error.message);
    process.exit(1);
});
process.on('unhandledRejection', (reason, promise) => {
    console.error(chalk_1.default.red('Unhandled Rejection at:'), promise, 'reason:', reason);
    process.exit(1);
});
// Graceful shutdown
process.on('SIGINT', () => {
    console.log(chalk_1.default.yellow('\nGracefully shutting down...'));
    process.exit(0);
});
program.parse();
//# sourceMappingURL=cli.js.map